import{C as H,r as s,D as V,j as d,P,F as p,I as z,A as q,_ as J}from"./index-sntk-7aJ.js";import{c as Q,u as W}from"./index-S3x6QFUG.js";var _="rovingFocusGroup.onEntryFocus",X={bubbles:!1,cancelable:!0},F="RovingFocusGroup",[h,G,Z]=Q(F),[$,le]=H(F,[Z]),[ee,te]=$(F),N=s.forwardRef((e,r)=>d.jsx(h.Provider,{scope:e.__scopeRovingFocusGroup,children:d.jsx(h.Slot,{scope:e.__scopeRovingFocusGroup,children:d.jsx(oe,{...e,ref:r})})}));N.displayName=F;var oe=s.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:c,orientation:t,loop:b=!1,dir:g,currentTabStopId:R,defaultCurrentTabStopId:E,onCurrentTabStopIdChange:m,onEntryFocus:a,preventScrollOnEntryFocus:C=!1,...w}=e,v=s.useRef(null),I=z(r,v),o=W(g),[l=null,T]=q({prop:R,defaultProp:E,onChange:m}),[u,i]=s.useState(!1),S=J(a),M=G(c),x=s.useRef(!1),[k,y]=s.useState(0);return s.useEffect(()=>{const n=v.current;if(n)return n.addEventListener(_,S),()=>n.removeEventListener(_,S)},[S]),d.jsx(ee,{scope:c,orientation:t,dir:o,loop:b,currentTabStopId:l,onItemFocus:s.useCallback(n=>T(n),[T]),onItemShiftTab:s.useCallback(()=>i(!0),[]),onFocusableItemAdd:s.useCallback(()=>y(n=>n+1),[]),onFocusableItemRemove:s.useCallback(()=>y(n=>n-1),[]),children:d.jsx(P.div,{tabIndex:u||k===0?-1:0,"data-orientation":t,...w,ref:I,style:{outline:"none",...e.style},onMouseDown:p(e.onMouseDown,()=>{x.current=!0}),onFocus:p(e.onFocus,n=>{const L=!x.current;if(n.target===n.currentTarget&&L&&!u){const D=new CustomEvent(_,X);if(n.currentTarget.dispatchEvent(D),!D.defaultPrevented){const A=M().filter(f=>f.focusable),U=A.find(f=>f.active),B=A.find(f=>f.id===l),Y=[U,B,...A].filter(Boolean).map(f=>f.ref.current);j(Y,C)}}x.current=!1}),onBlur:p(e.onBlur,()=>i(!1))})})}),O="RovingFocusGroupItem",K=s.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:c,focusable:t=!0,active:b=!1,tabStopId:g,...R}=e,E=V(),m=g||E,a=te(O,c),C=a.currentTabStopId===m,w=G(c),{onFocusableItemAdd:v,onFocusableItemRemove:I}=a;return s.useEffect(()=>{if(t)return v(),()=>I()},[t,v,I]),d.jsx(h.ItemSlot,{scope:c,id:m,focusable:t,active:b,children:d.jsx(P.span,{tabIndex:C?0:-1,"data-orientation":a.orientation,...R,ref:r,onMouseDown:p(e.onMouseDown,o=>{t?a.onItemFocus(m):o.preventDefault()}),onFocus:p(e.onFocus,()=>a.onItemFocus(m)),onKeyDown:p(e.onKeyDown,o=>{if(o.key==="Tab"&&o.shiftKey){a.onItemShiftTab();return}if(o.target!==o.currentTarget)return;const l=se(o,a.orientation,a.dir);if(l!==void 0){if(o.metaKey||o.ctrlKey||o.altKey||o.shiftKey)return;o.preventDefault();let u=w().filter(i=>i.focusable).map(i=>i.ref.current);if(l==="last")u.reverse();else if(l==="prev"||l==="next"){l==="prev"&&u.reverse();const i=u.indexOf(o.currentTarget);u=a.loop?ce(u,i+1):u.slice(i+1)}setTimeout(()=>j(u))}})})})});K.displayName=O;var re={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ne(e,r){return r!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function se(e,r,c){const t=ne(e.key,c);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(t))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(t)))return re[t]}function j(e,r=!1){const c=document.activeElement;for(const t of e)if(t===c||(t.focus({preventScroll:r}),document.activeElement!==c))return}function ce(e,r){return e.map((c,t)=>e[(r+t)%e.length])}var fe=N,de=K;export{de as I,fe as R,le as c};
