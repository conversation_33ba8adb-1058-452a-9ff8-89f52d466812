import{aA as o,j as t}from"./index-sntk-7aJ.js";import{I as p}from"./item-detail-form-C1jRhqxx.js";import"./form-2rWd-Izg.js";import"./pos-api-CQfNAror.js";import{b as e}from"./use-items-in-store-data-DdEUc9t0.js";import"./user-B67nntxu.js";import"./vietqr-api-C-lVxzD-.js";import"./crm-api-DhFa_vPG.js";import"./header-CePLmjHC.js";import"./main-BTno3738.js";import"./search-context-iBulh32Z.js";import"./date-range-picker-CBqQlAZr.js";import"./price-source-dialog-DzcYgTYK.js";import"./multi-select-SpNKBlxk.js";import"./exceljs.min-C2daZjFR.js";import"./core.esm-DJUjDuYb.js";import"./zod-DMJBVHr6.js";import"./use-upload-image-FpCPKTWt.js";import"./images-api-CY2_Ph94.js";import"./use-item-types-DlwgINfg.js";import"./useQuery-CPo_FvE_.js";import"./utils-km2FGkQ4.js";import"./useMutation-jqWRauQa.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-DVU3n-Lf.js";import"./use-units-DSbsiywC.js";import"./use-items-Dp-DpVBb.js";import"./item-api-CFTFT5cc.js";import"./use-removed-items-DUNjx3Y6.js";import"./use-customizations-Dvjv5zwl.js";import"./use-customization-by-id-lt4Zl_S3.js";import"./use-sources-DVPy3MTi.js";import"./sources-api-CATymHLl.js";import"./sources-CfiQ7039.js";import"./useCanGoBack-xjQXkRLG.js";import"./calendar-BhUTNdpd.js";import"./createLucideIcon-CvoWT756.js";import"./index-CyrU-3zB.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-gkM78Twn.js";import"./index-2BmiXKhT.js";import"./check-Dykbakem.js";import"./input-DCw8aMl6.js";import"./textarea-CUN2xTj-.js";import"./combobox-B4y2_kzV.js";import"./command-Ct8kkkRi.js";import"./dialog-HHE8oSxh.js";import"./search-DFsa4jPY.js";import"./popover-DUo0D-5L.js";import"./chevrons-up-down-BwW14lXu.js";import"./upload-C8ni-I8t.js";import"./collapsible-DwXQUepC.js";import"./confirm-dialog-CKbdgUwC.js";import"./alert-dialog-QV_IGTCu.js";import"./circle-help-DDGU4UNg.js";import"./select-ZzLBlgJd.js";import"./index-S3x6QFUG.js";import"./chevron-right-JsGDE6eB.js";import"./items-in-store-api-DAi66Dmg.js";import"./xlsx-DkH2s96g.js";import"./separator-BIRyiZJ0.js";import"./createReactComponent-BcntBX1O.js";import"./scroll-area-CPF3eFT1.js";import"./IconChevronRight-CuIdFTCw.js";import"./react-icons.esm-BTYMKzFL.js";import"./use-dialog-state-Ck6p_fTK.js";import"./modal-Yuq_-Dyf.js";import"./date-picker-GoQUD479.js";import"./calendar-Bccd0kH4.js";import"./badge-BulEkXWC.js";import"./circle-x-C-gqcnnq.js";const Rt=function(){const{id:r}=o({strict:!1}),{data:i,isLoading:m}=e(r,!!r);return m?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):i!=null&&i.data?t.jsx(p,{currentRow:i.data}):t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Không tìm thấy món ăn"})})})};export{Rt as component};
