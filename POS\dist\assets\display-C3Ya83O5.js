import{z as a,j as e,B as p}from"./index-sntk-7aJ.js";import{C as h}from"./content-section-BcjkwI3y.js";import{u,F as x,a as i,b as l,c as m,g as j,d as b,e as f}from"./form-2rWd-Izg.js";import{s as y}from"./zod-DMJBVHr6.js";import{C as F}from"./checkbox-gkM78Twn.js";import"./separator-BIRyiZJ0.js";import"./index-2BmiXKhT.js";import"./check-Dykbakem.js";import"./createLucideIcon-CvoWT756.js";const C=[{id:"recents",label:"Recents"},{id:"home",label:"Home"},{id:"applications",label:"Applications"},{id:"desktop",label:"Desktop"},{id:"downloads",label:"Downloads"},{id:"documents",label:"Documents"}],g=a.object({items:a.array(a.string()).refine(o=>o.some(s=>s),{message:"You have to select at least one item."})}),S={items:["recents","home"]};function D(){const o=u({resolver:y(g),defaultValues:S});return e.jsx(x,{...o,children:e.jsxs("form",{onSubmit:o.handleSubmit(s=>{}),className:"space-y-8",children:[e.jsx(i,{control:o.control,name:"items",render:()=>e.jsxs(l,{children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(m,{className:"text-base",children:"Sidebar"}),e.jsx(j,{children:"Select the items you want to display in the sidebar."})]}),C.map(s=>e.jsx(i,{control:o.control,name:"items",render:({field:t})=>{var r;return e.jsxs(l,{className:"flex flex-row items-start space-y-0 space-x-3",children:[e.jsx(b,{children:e.jsx(F,{checked:(r=t.value)==null?void 0:r.includes(s.id),onCheckedChange:c=>{var n;return c?t.onChange([...t.value,s.id]):t.onChange((n=t.value)==null?void 0:n.filter(d=>d!==s.id))}})}),e.jsx(m,{className:"font-normal",children:s.label})]},s.id)}},s.id)),e.jsx(f,{})]})}),e.jsx(p,{type:"submit",children:"Update display"})]})})}function v(){return e.jsx(h,{title:"Display",desc:"Turn items on or off to control what's displayed in the app.",children:e.jsx(D,{})})}const I=v;export{I as component};
