import{j as e,B as V,h as Y,r as b,a4 as v,u as W}from"./index-sntk-7aJ.js";import{g as se}from"./error-utils-DlcdSg51.js";import{c as oe,g as Z,a as ie,u as Q,b as le}from"./error-handler-DXT43YQQ.js";import{u as $}from"./use-stores-xf8_Gsi6.js";import{u as ce}from"./use-source-data-PlWXWdI1.js";import{H as he}from"./header-CePLmjHC.js";import{M as de}from"./main-BTno3738.js";import{P as me}from"./profile-dropdown-CteBDQaM.js";import{S as ue,T as pe}from"./search-BAjrNsV6.js";import{D as ge,a as xe,b as fe,c as G}from"./dropdown-menu-Cq_TXEG8.js";import{I as _e}from"./IconCopy-C1FNK1-Z.js";import{c as ye}from"./search-context-iBulh32Z.js";import{F as q}from"./filter-dropdown-DC1Lo7Eu.js";import{I as je}from"./IconPlus-CA3RKgcN.js";import{u as be,e as we,f as X}from"./index-C8dGDLCq.js";import{T as ee,a as te,b as K,c as A,d as ne,e as R}from"./table-BEvnnqp-.js";import{C as ke}from"./confirm-dialog-CKbdgUwC.js";import{I as Ne}from"./IconTrash-BHnBYU-I.js";import"./vietqr-api-C-lVxzD-.js";import{b as J}from"./pos-api-CQfNAror.js";import{S as Ce,a as ve,b as Te,c as Se,d as De}from"./select-ZzLBlgJd.js";import{P as ae}from"./modal-Yuq_-Dyf.js";import{read as Fe,utils as Pe}from"./xlsx-DkH2s96g.js";import{a as Ie}from"./use-payment-methods-Bcseb6o8.js";import"./user-B67nntxu.js";import"./crm-api-DhFa_vPG.js";import{E as Me}from"./exceljs.min-C2daZjFR.js";import{X as Re}from"./calendar-BhUTNdpd.js";import{U as re}from"./upload-C8ni-I8t.js";import{D as Ee}from"./download-DIRnyul3.js";import{C as Ae}from"./checkbox-gkM78Twn.js";import{D as Le,a as Oe,b as Ve,c as He,e as Ke}from"./dialog-HHE8oSxh.js";import{M as Ue}from"./multi-select-dropdown-BzYH8PW0.js";import{c as ze}from"./createLucideIcon-CvoWT756.js";import"./useQuery-CPo_FvE_.js";import"./utils-km2FGkQ4.js";import"./useMutation-jqWRauQa.js";import"./query-keys-3lmd-xp6.js";import"./stores-api-j41S6mEL.js";import"./separator-BIRyiZJ0.js";import"./date-range-picker-CBqQlAZr.js";import"./chevron-right-JsGDE6eB.js";import"./react-icons.esm-BTYMKzFL.js";import"./popover-DUo0D-5L.js";import"./index-CyrU-3zB.js";import"./isSameMonth-C8JQo-AN.js";import"./avatar-CcvCHous.js";import"./createReactComponent-BcntBX1O.js";import"./form-2rWd-Izg.js";import"./IconSearch-BFRP7UUl.js";import"./index-S3x6QFUG.js";import"./index-DBwfoC6_.js";import"./check-Dykbakem.js";import"./command-Ct8kkkRi.js";import"./search-DFsa4jPY.js";import"./scroll-area-CPF3eFT1.js";import"./IconChevronRight-CuIdFTCw.js";import"./chevrons-up-down-BwW14lXu.js";import"./alert-dialog-QV_IGTCu.js";import"./index-2BmiXKhT.js";import"./payment-methods-api-BMP84Pfb.js";import"./input-DCw8aMl6.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],We=ze("arrow-right",Be);function $e({onCopyChannels:n,onImportFromFile:t}){return e.jsxs(ge,{children:[e.jsx(xe,{asChild:!0,children:e.jsx(V,{variant:"outline",size:"sm",children:"Tiện ích"})}),e.jsxs(fe,{align:"end",children:[e.jsxs(G,{onClick:n,children:[e.jsx(_e,{className:"mr-2 h-4 w-4"}),"Sao chép KBH"]}),e.jsxs(G,{onClick:t,children:[e.jsx(ye,{className:"mr-2 h-4 w-4"}),"Thêm kênh bán hàng từ file"]})]})]})}function qe({value:n,onValueChange:t,sourceOptions:r,isLoading:i,placeholder:s="Tất cả nguồn",className:d="w-48"}){const l=(r==null?void 0:r.map(o=>({value:o.value,label:o.label})))||[];return e.jsx(q,{value:n,onValueChange:t,options:l,isLoading:i,placeholder:s,className:d,allOptionLabel:"Tất cả nguồn",loadingText:"Đang tải nguồn...",emptyText:"Không có nguồn"})}function Ge({value:n,onValueChange:t,stores:r,isLoading:i,placeholder:s="Tất cả điểm",className:d="w-48"}){const l=(r==null?void 0:r.map(o=>({value:o.id,label:o.name})))||[];return e.jsx(q,{value:n,onValueChange:t,options:l,isLoading:i,placeholder:s,className:d,allOptionLabel:"Tất cả điểm",loadingText:"Đang tải...",emptyText:"Không có cửa hàng"})}function Xe({selectedStore:n,onStoreChange:t,selectedSource:r,onSourceChange:i,stores:s,sourceOptions:d,storesLoading:l,sourcesLoading:o,onCreateChannel:m,onCopyChannels:u,onImportFromFile:y}){return e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Kênh bán hàng"}),e.jsx(Ge,{value:n,onValueChange:t,stores:s,isLoading:l}),e.jsx(qe,{value:r,onValueChange:i,sourceOptions:d,isLoading:o})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($e,{onCopyChannels:u,onImportFromFile:y}),e.jsxs(V,{size:"sm",onClick:m,children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),"Thêm kênh bán hàng"]})]})]})}function Je({columns:n,data:t}){var d;const r=Y(),i=be({data:t,columns:n,getCoreRowModel:we()}),s=l=>{r({to:`/sale-channel/channel/detail/${l.id}`})};return e.jsx("div",{className:"rounded-md border",children:e.jsxs(ee,{children:[e.jsx(te,{children:i.getHeaderGroups().map(l=>e.jsx(K,{children:l.headers.map(o=>e.jsx(A,{children:o.isPlaceholder?null:X(o.column.columnDef.header,o.getContext())},o.id))},l.id))}),e.jsx(ne,{children:(d=i.getRowModel().rows)!=null&&d.length?i.getRowModel().rows.map(l=>e.jsx(K,{"data-state":l.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:()=>s(l.original),children:l.getVisibleCells().map(o=>e.jsx(R,{children:X(o.column.columnDef.cell,o.getContext())},o.id))},l.id)):e.jsx(K,{children:e.jsx(R,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu kênh bán hàng."})})})]})})}function Ye({channel:n}){const[t,r]=b.useState(!1),[i,s]=b.useState(!1),d=m=>{m.stopPropagation(),m.preventDefault(),m.nativeEvent.stopImmediatePropagation(),r(!0)},l=m=>{r(m)},o=async()=>{s(!0);try{await oe.deleteChannel(n.id),v.success("Xóa kênh bán hàng thành công!"),r(!1),window.location.reload()}catch(m){const u=Z(m),y=ie(m);console.error("Error deleting channel:",m),y&&console.error("Track ID:",y),v.error(u),r(!1)}finally{s(!1)}};return e.jsxs("div",{onClick:m=>m.stopPropagation(),children:[e.jsx(V,{variant:"ghost",size:"sm",onClick:d,className:"h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700",children:e.jsx(Ne,{className:"h-4 w-4"})}),e.jsx(ke,{open:t,onOpenChange:l,title:"Xác nhận xóa kênh bán hàng",desc:`Bạn có chắc chắn muốn xóa kênh "${n.source_name}"? Hành động này không thể hoàn tác.`,confirmText:"Xóa",cancelBtnText:"Hủy",destructive:!0,isLoading:i,handleConfirm:o})]})}function Ze({channel:n}){const t=r=>{r.stopPropagation()};return e.jsx("div",{className:"flex items-center justify-end",onClick:t,children:e.jsx(Ye,{channel:n})})}const Qe=[{accessorKey:"id",header:"#",cell:({row:n})=>{const t=n.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:t})},enableSorting:!1},{accessorKey:"source_name",header:"Kênh",cell:({row:n})=>{const t=n.original;return e.jsx("span",{className:"font-medium",children:t.source_name})}},{accessorKey:"stores.store_name",header:"Cửa hàng",cell:({row:n})=>{const t=n.original;return e.jsx("span",{className:"font-medium",children:t.stores.store_name})}},{accessorKey:"extra_data.commission",header:"Phần trăm hoa hồng",cell:({row:n})=>{const r=n.original.extra_data.commission*100;return e.jsxs("span",{className:"font-medium",children:[r,"%"]})}},{accessorKey:"extra_data.payment_method_name",header:"PTTT",cell:({row:n})=>{const t=n.original,r=t.extra_data.payment_type,i=t.extra_data.payment_method_name;return e.jsxs("span",{className:"font-medium",children:[r==="POSTPAID"?"Trả sau":"Trả trước"," - ",i]})}},{id:"actions",header:"",cell:({row:n})=>e.jsx(Ze,{channel:n.original}),enableSorting:!1}],et=()=>{const n=new Me.Workbook;return n.creator="POS System",n.lastModifiedBy="POS System",n.created=new Date,n.modified=new Date,n},tt=n=>{const t=n.extra_data||{};return[n.source_id||"",t.commission||0,t.not_show_partner_bill||0,t.use_order_online||0,t.exclude_ship||0,t.payment_type||"POSTPAID",t.payment_method_id||"",t.require_tran_no||0,t.marketing_partner_cost_type||"AMOUNT",t.marketing_partner_cost||0,t.voucher_run_partner||"",t.marketing_partner_cost_from_date?new Date(t.marketing_partner_cost_from_date*1e3).toLocaleDateString("vi-VN"):"",t.marketing_partner_cost_to_date?new Date(t.marketing_partner_cost_to_date*1e3).toLocaleDateString("vi-VN"):"",t.marketing_partner_cost_date_week||0,t.marketing_partner_cost_hour_day||0]},nt=(n,t)=>{const r=n.addWorksheet("Sheet"),i=["Mã nguồn","Phần trăm hoa hồng","Sử dụng hóa đơn dành cho khách hàng khi in","Áp dụng đơn online","Không tính phí vận chuyển vào doanh thu","Hình thức thanh toán","Mã PTTT","Nhập số hoá đơn đối tác","Loại chi phí","Số tiền|Phần trăm / đơn","Voucher","Ngày bắt đầu","Ngày kết thúc","Chọn ngày","Chọn giờ"];return r.addRow(i).eachCell(l=>{l.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},l.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},l.alignment={horizontal:"center",vertical:"middle"},l.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),t.forEach(l=>{const o=tt(l);r.addRow(o).eachCell(u=>{u.font={size:10},u.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),[17.3,20.6,44.5,19.3,42.4,21.7,50.1,24.2,12.6,23.6,11.9,15,15,11.3,9.5].forEach((l,o)=>{r.getColumn(o+1).width=l}),r},at=(n,t=[])=>{var P,D,F,C,I,S,M,c;const r=n.addWorksheet("Example");r.addRow(["Đây là sheet mẫu để tham khảo. Vui lòng quay lại sheet 1 để nhập dữ liệu."]);const i=["Mã nguồn","Phần trăm hoa hồng","Sử dụng hóa đơn dành cho khách hàng khi in","Áp dụng đơn online","Không tính phí vận chuyển vào doanh thu","Hình thức thanh toán","Mã PTTT","Nhập số hoá đơn đối tác","Loại chi phí","Số tiền|Phần trăm / đơn","Voucher","Ngày bắt đầu","Ngày kết thúc","Chọn ngày","Chọn giờ"];r.addRow(i).eachCell(a=>{a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},a.font={bold:!0,size:11},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),[["SOURCE-AE3F",0,"0 (Không sử dụng hoá đơn dành cho khách khi in)",0,0,"PREPAID (Trả trước)","(Không cần điền với trả trước)",0,"AMOUNT",1e4,"","01-02-2023​","01-03-2023​",38,1324],["SOURCE-1XZ2",20,"1 (Sử dụng hoá đơn dành cho khách khi in)",1,1,"POSTPAID (Trả sau)","MASTER",1,"PERCENT",25,"","","01-03-2023​",38,8264],["SOURCE-H250",40,"2 (In hóa đơn chỉ với phần giảm giá nhà hàng)","","","POSTPAID (Trả sau)","FABI_DEBT","","AMOUNT","","","01-02-2023​","",64,8193]].forEach(a=>{r.addRow(a)}),r.addRow([]),r.addRow(["Bảng tham chiếu nguồn","","","Bảng tham chiếu PTTT","","","BẢNG THAM CHIẾU NGÀY","","","BẢNG THAM CHIẾU GIỜ"]).eachCell(a=>{a.value&&a.value.toString().trim()!==""&&(a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},a.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})}),r.addRow(["Tên nguồn","Mã nguồn","","Tên PTTT","Mã PTTT","","Thời gian","Giá trị","","Thời gian","Giá trị"]).eachCell(a=>{a.value&&a.value.toString().trim()!==""&&(a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF79ABE3"}},a.font={bold:!0,size:10},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}})});const m=[],u=[],y=new Map,w=new Map;t.forEach(a=>{a.source_id&&a.source_name&&y.set(a.source_id,a.source_name);const h=a.extra_data||{};h.payment_method_id&&h.payment_method_name&&w.set(h.payment_method_id,h.payment_method_name)}),Array.from(y.entries()).forEach(([a,h])=>{m.push([`${h} (nguồn đã chọn)`,a])}),Array.from(w.entries()).forEach(([a,h])=>{u.push([h,a])});const x=[["Chủ nhật","2"],["Thứ 2","4"],["Thứ 3","8"],["Thứ 4","16"],["Thứ 5","32"],["Thứ 6","64"],["Thứ 7","128"],["Ví dụ: CN, T2, T5 = 2 + 4 + 32","38"]],p=[["0h","1"],["1h","2"],["2h","4"],["3h","8"],["4h","16"],["5h","32"],["6h","64"],["7h","128"],["8h","256"],["9h","512"],["10h","1024"],["11h","2048"],["12h","4096"],["13h","8192"],["14h","16384"],["15h","32768"],["16h","65536"],["17h","131072"],["18h","262144"],["19h","524288"],["20h","1048576"],["21h","2097152"],["22h","4194304"],["23h","8388608"],["Ví dụ: 0h, 1h, 3h = 1 + 2 + 8","11"]],T=Math.max(m.length,u.length,x.length,p.length),j=[];for(let a=0;a<T;a++){const h=[((P=m[a])==null?void 0:P[0])||"",((D=m[a])==null?void 0:D[1])||"","",((F=u[a])==null?void 0:F[0])||"",((C=u[a])==null?void 0:C[1])||"","",((I=x[a])==null?void 0:I[0])||"",((S=x[a])==null?void 0:S[1])||"","",((M=p[a])==null?void 0:M[0])||"",((c=p[a])==null?void 0:c[1])||""];j.push(h)}return j.forEach(a=>{r.addRow(a).eachCell(g=>{g.value&&g.value.toString().trim()!==""&&(g.font={size:10},g.alignment={horizontal:"left",vertical:"middle"},g.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}})})}),[25.8,20.6,49.2,19.3,42.4,21.7,29.2,24.2,12.6,23.6,9,16.1,16.1,11.3,9.5].forEach((a,h)=>{r.getColumn(h+1).width=a}),r},rt=(n,t,r,i)=>n.map(s=>({source_id:s.source_id,source_name:s.source_id,description:null,active:1,sort:0,is_fabi:1,source_type:["ONLINE"],company_uid:t,brand_uid:r,store_uid:i,partner_config:1,extra_data:{require_tran_no:s.require_tran_no,commission:s.commission/100,deduct_tax_rate:0,payment_method_id:s.payment_method_id,payment_method_name:s.payment_method_id,payment_type:s.payment_type,marketing_partner_cost_type:s.marketing_partner_cost_type,marketing_partner_cost:s.marketing_partner_cost,voucher:s.voucher_run_partner||"",not_show_partner_bill:s.not_show_partner_bill,use_order_online:s.use_order_online,exclude_ship:s.exclude_ship,marketing_partner_cost_from_date:new Date(s.marketing_partner_cost_from_date).getTime()*1e3,marketing_partner_cost_to_date:new Date(s.marketing_partner_cost_to_date).getTime()*1e3,marketing_partner_cost_date_week:s.marketing_partner_cost_date_week,marketing_partner_cost_hour_day:s.marketing_partner_cost_hour_day}}));function st(){const[n,t]=b.useState(!1),[r,i]=b.useState(null),[s,d]=b.useState([]),[l,o]=b.useState(!1),[m,u]=b.useState([]),[y,w]=b.useState(!1),x=b.useRef(null),{company:p,brands:T}=W(h=>h.auth),j=T==null?void 0:T[0],{data:E=[]}=Ie({storeUid:"all"}),P=h=>{const g=[],N=new Set,_=new Set(E.map(f=>f.code));return h.forEach((f,L)=>{const O=L+2;N.has(f.source_id)?g.push({row:O,message:`Mã nguồn "${f.source_id}" bị trùng lặp`}):N.add(f.source_id),f.payment_type==="POSTPAID"&&f.payment_method_id&&(_.has(f.payment_method_id)||g.push({row:O,message:`Phương thức thanh toán "${f.payment_method_id}" không hợp lệ`})),(!f.source_id||f.source_id.trim()==="")&&g.push({row:O,message:"Mã nguồn không được để trống"}),(!f.payment_type||f.payment_type!=="PREPAID"&&f.payment_type!=="POSTPAID")&&g.push({row:O,message:"Hình thức thanh toán phải là PREPAID hoặc POSTPAID"})}),g},D=()=>{var h;(h=x.current)==null||h.click()},F=h=>{var N;const g=(N=h.target.files)==null?void 0:N[0];if(g){if(!g.name.endsWith(".xlsx")&&!g.name.endsWith(".xls")){v.error("Vui lòng chọn file Excel (.xlsx hoặc .xls)");return}i(g),C(g)}},C=h=>{t(!0);const g=new FileReader;g.onload=N=>{var _;try{const f=new Uint8Array((_=N.target)==null?void 0:_.result),L=Fe(f,{type:"array"}),O=L.SheetNames[0],U=L.Sheets[O],z=Pe.sheet_to_json(U,{header:1}).slice(1).filter(k=>k[0]).map(k=>({source_id:k[0]||"",commission:parseFloat(k[1])||0,not_show_partner_bill:parseInt(k[2])||0,use_order_online:parseInt(k[3])||0,exclude_ship:parseInt(k[4])||0,payment_type:k[5]||"POSTPAID",payment_method_id:k[6]||"",require_tran_no:parseInt(k[7])||0,marketing_partner_cost_type:k[8]||"AMOUNT",marketing_partner_cost:parseFloat(k[9])||0,voucher_run_partner:k[10]||"",marketing_partner_cost_from_date:k[11]||"",marketing_partner_cost_to_date:k[12]||"",marketing_partner_cost_date_week:parseInt(k[13])||0,marketing_partner_cost_hour_day:parseInt(k[14])||0})),B=P(z);if(u(B),B.length>0){u(B),w(!0);return}w(!1),u([]),d(z),o(!0),v.success(`Đã phân tích ${z.length} kênh bán hàng từ file`)}catch{v.error("Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.")}finally{t(!1)}},g.onerror=()=>{v.error("Lỗi khi đọc file"),t(!1)},g.readAsArrayBuffer(h)},I=async h=>{var g;if(!(p!=null&&p.id)||!(j!=null&&j.id)||!h){v.error("Vui lòng chọn cửa hàng trước khi tải file");return}try{t(!0);const N=await J.get("/mdata/v1/channels",{params:{skip_limit:"true",company_uid:p.id,brand_uid:j.id,store_uid:h}}),_=Array.isArray((g=N.data)==null?void 0:g.data)?N.data.data:[],f=et();nt(f,_),at(f,_);try{const L=await f.xlsx.writeBuffer(),O=new Blob([L],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),U=window.URL.createObjectURL(O),H=document.createElement("a");H.href=U,H.download="import-sale-channel.xlsx",H.style.display="none",document.body.appendChild(H),H.click(),document.body.removeChild(H),window.URL.revokeObjectURL(U)}catch(L){console.log("ExcelJS download failed:",L),v.error("Lỗi khi tạo file Excel");return}}catch{v.error("Lỗi khi tải file dữ liệu kênh bán hàng")}finally{t(!1)}},S=async h=>{if(!(p!=null&&p.id)||!(j!=null&&j.id)||!h||s.length===0){v.error("Không có dữ liệu để import");return}try{t(!0);const g=rt(s,p.id,j.id,h);for(const N of g)await J.post("/mdata/v1/channels",[N]);return v.success(`Đã import thành công ${g.length} kênh bán hàng`),!0}catch{return v.error("Lỗi khi import dữ liệu"),!1}finally{t(!1)}},M=()=>{w(!1),u([]),i(null),x.current&&(x.current.value=""),D()},c=h=>{h||(w(!1),u([]),i(null),x.current&&(x.current.value=""))},a=b.useCallback(()=>{i(null),d([]),o(!1),u([]),w(!1),x.current&&(x.current.value="")},[]);return{isLoading:n,selectedFile:r,parsedData:s,showImportParsedData:l,validationErrors:m,showValidationModal:y,fileInputRef:x,handleFileUpload:D,handleFileChange:F,handleValidationRetry:M,handleValidationModalClose:c,downloadTemplate:I,importData:S,resetState:a}}function ot({data:n}){return n.length===0?e.jsx("div",{className:"flex h-32 items-center justify-center text-gray-500",children:"Không có dữ liệu để hiển thị"}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-lg font-medium",children:["Xem trước dữ liệu (",n.length," kênh bán hàng)"]}),e.jsx("div",{className:"max-h-96 overflow-auto rounded-lg border",children:e.jsxs(ee,{children:[e.jsx(te,{children:e.jsxs(K,{children:[e.jsx(A,{className:"min-w-[120px]",children:"Mã nguồn"}),e.jsx(A,{className:"min-w-[100px]",children:"Hoa hồng (%)"}),e.jsx(A,{className:"min-w-[120px]",children:"Hóa đơn khách"}),e.jsx(A,{className:"min-w-[100px]",children:"Đơn online"}),e.jsx(A,{className:"min-w-[120px]",children:"Loại thanh toán"}),e.jsx(A,{className:"min-w-[120px]",children:"Mã PTTT"}),e.jsx(A,{className:"min-w-[100px]",children:"Loại chi phí"}),e.jsx(A,{className:"min-w-[120px]",children:"Chi phí"})]})}),e.jsx(ne,{children:n.map((t,r)=>e.jsxs(K,{children:[e.jsx(R,{className:"font-medium",children:t.source_id}),e.jsxs(R,{children:[t.commission,"%"]}),e.jsx(R,{children:t.not_show_partner_bill===0?"Không sử dụng":t.not_show_partner_bill===1?"Sử dụng":"Chỉ giảm giá"}),e.jsx(R,{children:t.use_order_online?"Có":"Không"}),e.jsx(R,{children:t.payment_type==="PREPAID"?"Trả trước":"Trả sau"}),e.jsx(R,{children:t.payment_method_id}),e.jsx(R,{children:t.marketing_partner_cost_type==="AMOUNT"?"Số tiền":"Phần trăm"}),e.jsx(R,{children:t.marketing_partner_cost_type==="AMOUNT"?`${t.marketing_partner_cost.toLocaleString()} VNĐ`:`${t.marketing_partner_cost}%`})]},r))})]})})]})}function it({open:n,onOpenChange:t,errors:r,onRetry:i,onClose:s,storeName:d="cửa hàng"}){const l=()=>{t(!1),i()},o=r[0],m=o?`Dòng ${o.row}: ${o.message}`:"Dữ liệu không hợp lệ",u=y=>{y||s==null||s(),t(y)};return e.jsx(ae,{title:"Lỗi dữ liệu",open:n,onOpenChange:u,onCancel:()=>s==null?void 0:s(),onConfirm:()=>{},hideButtons:!0,maxWidth:"sm:max-w-md",centerTitle:!0,children:e.jsxs("div",{className:"flex flex-col items-center space-y-6 py-4",children:[e.jsx("div",{className:"flex h-16 w-16 items-center justify-center rounded-full bg-red-100",children:e.jsx(Re,{className:"h-8 w-8 text-red-600"})}),e.jsx("div",{className:"text-center",children:e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:["Thêm kênh bán hàng cho ",d]})}),e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"font-medium text-red-600",children:m})}),e.jsxs(V,{onClick:l,className:"flex items-center gap-2 bg-blue-600 hover:bg-blue-700",children:[e.jsx(re,{className:"h-4 w-4"}),"Tải file lên"]})]})})}function lt({open:n,onOpenChange:t,onSuccess:r}){const[i,s]=b.useState(""),{company:d,brands:l}=W(_=>_.auth),o=l==null?void 0:l[0],{data:m=[]}=$({params:{company_uid:(d==null?void 0:d.id)||"",brand_uid:(o==null?void 0:o.id)||""},enabled:!!(d!=null&&d.id)&&!!(o!=null&&o.id)}),{isLoading:u,selectedFile:y,parsedData:w,showImportParsedData:x,validationErrors:p,showValidationModal:T,fileInputRef:j,handleFileUpload:E,handleFileChange:P,handleValidationRetry:D,handleValidationModalClose:F,downloadTemplate:C,importData:I,resetState:S}=st();b.useEffect(()=>{!n&&!u&&(s(""),S())},[n,S,u]);const M=async()=>{if(i)try{await C(i)}catch(_){console.error("Download template error:",_)}},c=async()=>{if(x)try{await I(i)&&(r==null||r(),t(!1))}catch(_){console.error("Import data error:",_)}else return},a=()=>{t(!1)},h=()=>x?"Tải file lên":"Tiếp tục",g=()=>{const _=m.find(f=>f.id===i);return(_==null?void 0:_.name)||"cửa hàng"},N=()=>x?w.length===0:!1;return e.jsxs(e.Fragment,{children:[e.jsx(ae,{title:"Thêm kênh bán hàng cho cửa hàng",open:n&&!T||x,onOpenChange:t,onCancel:a,onConfirm:c,confirmText:h(),cancelText:"Hủy",centerTitle:!0,maxWidth:x?"sm:max-w-6xl":"sm:max-w-[600px]",isLoading:u,confirmDisabled:N(),hideButtons:!x,children:e.jsx("div",{className:"space-y-6",children:x?e.jsx(ot,{data:w}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-3 rounded-lg bg-gray-50 p-4",children:[e.jsx("div",{className:"text-lg font-medium",children:"Bước 1. Chọn cửa hàng"}),e.jsxs(Ce,{value:i,onValueChange:s,children:[e.jsx(ve,{className:"w-full",children:e.jsx(Te,{placeholder:"Chọn cửa hàng"})}),e.jsx(Se,{children:m.map(_=>e.jsx(De,{value:_.id,children:_.name},_.id))})]})]}),e.jsxs("div",{className:"space-y-3 rounded-lg bg-gray-50 p-4",children:[e.jsx("div",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu kênh bán hàng đã có."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"}),e.jsx(V,{variant:"outline",size:"sm",onClick:()=>{M()},disabled:!i||u,className:"flex items-center gap-2",children:e.jsx(Ee,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 rounded-lg bg-gray-50 p-4",children:[e.jsx("div",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Thêm hoặc sửa thông tin kênh bán hàng dựa vào sheet mẫu."})]}),e.jsxs("div",{className:"space-y-3 rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"})]}),e.jsxs(V,{variant:"outline",onClick:E,disabled:u,className:"flex items-center gap-2",children:[e.jsx(re,{className:"h-4 w-4"}),"Tải file lên"]})]}),y&&e.jsxs("div",{className:"text-sm text-green-600",children:["Đã chọn file: ",y.name]})]})]})})}),e.jsx("input",{ref:j,type:"file",accept:".xlsx,.xls",onChange:P,style:{display:"none"}}),e.jsx(it,{open:T,onOpenChange:F,errors:p,onRetry:D,onClose:()=>{t(!1)},storeName:g()})]})}function ct({open:n,onOpenChange:t}){const{company:r,brands:i}=W(c=>c.auth),s=i==null?void 0:i[0],[d,l]=b.useState(""),[o,m]=b.useState([]),[u,y]=b.useState([]),{data:w,isLoading:x}=$({params:{company_uid:(r==null?void 0:r.id)||"",brand_uid:(s==null?void 0:s.id)||""},enabled:!!(r!=null&&r.id)&&!!(s!=null&&s.id)}),{data:p,isLoading:T}=Q({storeId:d,enabled:!!d}),j=le({onSuccess:()=>{v.success("Sao chép kênh bán hàng thành công"),S()},onError:c=>{const a=Z(c);v.error(a)}});b.useEffect(()=>{n||(l(""),m([]),y([]))},[n]);const E=w?w.map(c=>({value:c.id,label:c.name})):[],P=w?w.filter(c=>c.id!==d).map(c=>({value:c.id,label:c.name})):[],D=c=>{l(c),m([])},F=c=>{m(a=>a.includes(c)?a.filter(h=>h!==c):[...a,c])},C=c=>{y(c)},I=async()=>{if(p)try{await j.mutateAsync({sourceStoreId:d,channelIds:o,targetStoreIds:u,channels:p})}catch(c){console.error("Error copying channels:",c)}},S=()=>{t(!1)},M=o.length===0||u.length===0||j.isPending;return e.jsx(Le,{open:n,onOpenChange:t,children:e.jsxs(Oe,{className:"sm:max-w-[600px]",children:[e.jsx(Ve,{children:e.jsx(He,{className:"text-center text-lg font-medium",children:"Sao chép kênh bán hàng"})}),e.jsxs("div",{className:"space-y-6 py-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(q,{value:d,onValueChange:D,options:E,isLoading:x,placeholder:"Chọn cửa hàng nguồn",className:"w-full",showAllOption:!1})}),e.jsx("div",{className:"flex-1",children:e.jsx(Ue,{options:P,value:u,onValueChange:C,placeholder:"Chọn cửa hàng đích",searchPlaceholder:"Tìm kiếm",isLoading:x,disabled:!d,showSelectAll:!0,selectAllLabel:"Chọn tất cả"})})]}),e.jsxs("div",{className:"flex items-start gap-6",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("h3",{className:"text-sm font-medium mb-2",children:["Kênh bán hàng nguồn ",p!=null&&p.length?`(${p.length})`:""]}),e.jsx("div",{className:"min-h-[200px] space-y-3",children:T?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Đang tải..."})}):d?(p==null?void 0:p.length)===0?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không có kênh bán hàng"})}):p==null?void 0:p.map(c=>e.jsxs("div",{className:"flex cursor-pointer items-center space-x-3 hover:bg-gray-50 hover:shadow-sm p-2 rounded border border-transparent hover:border-gray-200 transition-all duration-150",onClick:()=>F(c.id),children:[e.jsx(Ae,{checked:o.includes(c.id),className:"h-5 w-5 pointer-events-none"}),e.jsx("span",{className:"text-sm font-medium select-none",children:c.source_name})]},c.id)):e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Chọn cửa hàng để thực hiện sao chép"})})})]}),e.jsx("div",{className:"flex items-center justify-center pt-8",children:e.jsx(We,{className:"text-muted-foreground h-8 w-8"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("h3",{className:"text-sm font-medium mb-2",children:["Kênh sẽ được sao chép ",o.length?`(${o.length})`:""]}),e.jsx("div",{className:"min-h-[200px] space-y-3",children:o.length===0?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("p",{className:"text-muted-foreground text-sm",children:"Chọn kênh bán hàng từ cửa hàng nguồn"})}):p==null?void 0:p.filter(c=>o.includes(c.id)).map(c=>e.jsx("div",{className:"text-sm font-medium",children:c.source_name},c.id))})]})]})]}),e.jsx(Ke,{children:e.jsxs("div",{className:"flex w-full justify-between",children:[e.jsx(V,{type:"button",variant:"outline",onClick:S,disabled:j.isPending,children:"Huỷ"}),e.jsx(V,{type:"button",onClick:I,disabled:M,className:"bg-blue-600 text-white hover:bg-blue-700",children:j.isPending?"Đang xử lý...":"Sao chép"})]})})]})})}function ht(){const n=Y(),[t,r]=b.useState("all"),[i,s]=b.useState("all"),[d,l]=b.useState(!1),[o,m]=b.useState(!1),{data:u,isLoading:y,error:w}=Q(),{data:x,isLoading:p,error:T}=$(),{sourceOptions:j,isLoading:E,error:P}=ce(),D=y||p||E,F=w||T||P;let C=u||[];t!=="all"&&(C=C.filter(a=>a.store_uid===t)),i!=="all"&&(C=C.filter(a=>a.source_id===i));const I=()=>{n({to:"/sale-channel/channel/detail"})},S=()=>{l(!0)},M=()=>{m(!0)},c=()=>{window.location.reload()};return e.jsxs(e.Fragment,{children:[e.jsx(he,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ue,{}),e.jsx(pe,{}),e.jsx(me,{})]})}),e.jsx(de,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Xe,{selectedStore:t,onStoreChange:r,selectedSource:i,onSourceChange:s,stores:x,sourceOptions:j,storesLoading:p,sourcesLoading:E,onCreateChannel:I,onCopyChannels:S,onImportFromFile:M}),F?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:se(F)})}):D?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu kênh bán hàng..."})}):e.jsx(Je,{columns:Qe,data:C}),e.jsx(ct,{open:d,onOpenChange:l}),e.jsx(lt,{open:o,onOpenChange:m,onSuccess:c})]})})]})}const bn=ht;export{bn as component};
