import{E as x}from"./exceljs.min-C2daZjFR.js";import"./index-sntk-7aJ.js";async function L(F,w){const{filename:v="sale-sources.xlsx"}=w,b=new x.Workbook,r=b.addWorksheet("Nguồn đơn hàng"),p=new Set;F.forEach(t=>{t.list_data&&Array.isArray(t.list_data)&&t.list_data.forEach(e=>{e.date&&p.add(e.date)})});const c=Array.from(p).sort(),m=["","Tổng"],y=["Nguồn","Tổng hoá đơn","Giảm giá","<PERSON>anh thu (net)"];c.forEach(t=>{const e=new Date(t).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"});m.push(e,e),y.push("Tổng hoá đơn","Giảm giá","<PERSON><PERSON><PERSON> thu (net)","Tổng hoá đơn","Giảm giá","Doanh thu (net)")});const f=4+c.length*6,_=[];for(let t=0;t<f;t++)_.push({header:"",key:`col_${t}`,width:t===0?25:15});r.columns=_;const a=r.getRow(1);a.height=30,a.font={bold:!0,color:{argb:"FFFFFF"}},a.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},a.alignment={horizontal:"center",vertical:"middle"},a.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},m.forEach((t,e)=>{const o=a.getCell(e+1);o.value=t,e===0?(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF"}},o.font={bold:!0,color:{argb:"000000"}}):e===1?(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},o.font={bold:!0,color:{argb:"FFFFFF"}}):(e-2)%2===0?(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},o.font={bold:!0,color:{argb:"FFFFFF"}}):(o.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF8C00"}},o.font={bold:!0,color:{argb:"FFFFFF"}})}),f>=4&&r.mergeCells(1,2,1,4);let g=5;c.forEach(()=>{const t=g,e=g+5;e<=f&&r.mergeCells(1,t,1,e),g=e+1});const i=r.getRow(2);i.height=30,i.font={bold:!0,color:{argb:"000000"}},i.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF"}},i.alignment={horizontal:"center",vertical:"middle"},i.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},y.forEach((t,e)=>{const o=i.getCell(e+1);o.value=t}),F.forEach((t,e)=>{const o={col_0:t.source_name},h={total_bill:t.total_bill??0,discount_amount:t.discount_amount??0,revenue_net:t.revenue_net??0};o.col_1=h.total_bill,o.col_2=h.discount_amount,o.col_3=h.revenue_net;let l=4;c.forEach(s=>{const n=(t.list_data||[]).find(R=>R.date===s);l+5<f&&(o[`col_${l}`]=(n==null?void 0:n.total_bill)||0,l++,o[`col_${l}`]=(n==null?void 0:n.discount_amount)||0,l++,o[`col_${l}`]=(n==null?void 0:n.revenue_net)||0,l++,o[`col_${l}`]=0,l++,o[`col_${l}`]=0,l++,o[`col_${l}`]=0,l++)});const d=r.addRow(o);d.alignment={horizontal:"center",vertical:"middle"},e%2===0?d.fill={type:"pattern",pattern:"solid",fgColor:{argb:"F8F9FA"}}:d.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFFFFF"}},d.eachCell(s=>{s.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},typeof s.value=="number"&&(s.numFmt="#,##0",s.font={color:{argb:"000000"}})})});const E=await b.xlsx.writeBuffer(),k=new Blob([E],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),C=window.URL.createObjectURL(k),u=document.createElement("a");u.href=C,u.download=v,u.click(),window.URL.revokeObjectURL(C)}export{L as exportSourceRevenueToExcel};
