import{u as i}from"./useQuery-CPo_FvE_.js";import{a}from"./pos-api-CQfNAror.js";import{Q as s}from"./query-keys-3lmd-xp6.js";const o=async()=>{try{const e=await a.get("/v1/mdata/cities");if(Array.isArray(e.data))return e.data.filter(t=>t.active===1);if(e.data.success===!1)throw new Error(e.data.message||"Failed to fetch cities");if(e.data.data&&Array.isArray(e.data.data))return e.data.data.filter(t=>t.active===1);throw new Error("Unexpected response structure")}catch(e){throw e instanceof Error?new Error(`Failed to fetch cities: ${e.message}`):new Error("Failed to fetch cities")}};function d(){return i({queryKey:[s.CITIES],queryFn:o,staleTime:5*60*1e3,gcTime:10*60*1e3})}export{d as u};
