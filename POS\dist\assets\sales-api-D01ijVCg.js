import{b as D}from"./pos-api-CQfNAror.js";const y=5*60*1e3,l=new Map,S=new Map,u={get:t=>{const e=l.get(t);return e&&Date.now()-e.timestamp<y?e.data:null},set:(t,e)=>{l.set(t,{data:e,timestamp:Date.now()})},getPendingRequest:t=>S.get(t)||null,setPendingRequest:(t,e)=>{S.set(t,e)},removePendingRequest:t=>{S.delete(t)},generateKey:t=>`sales-${t.companyUid}-${t.brandUid}-${t.listStoreUid}-${t.startDate}-${t.endDate}-${t.page||1}-${t.sourceId||""}-${t.isDiscount||""}`,clear:()=>{l.clear(),S.clear()},clearExpired:()=>{const t=Date.now();for(const[e,o]of l.entries())t-o.timestamp>=y&&l.delete(e)},getStats:()=>({cacheSize:l.size,pendingRequests:S.size,cacheDuration:y})};setInterval(()=>{u.clearExpired()},10*60*1e3);const g={getSalesReport:async t=>{const e=u.generateKey(t),o=u.get(e);if(o)return o;const n=u.getPendingRequest(e);if(n)return n;const c=(async()=>{try{const r=new URLSearchParams({company_uid:t.companyUid,brand_uid:t.brandUid,list_store_uid:t.listStoreUid,start_date:t.startDate.toString(),end_date:t.endDate.toString(),page:(t.page||1).toString()});t.sourceId!==void 0&&r.append("source_id",t.sourceId.toString()),t.isDiscount!==void 0&&r.append("is_discount",t.isDiscount.toString());const s=(await D.get(`/v1/accounting/report/sale?${r.toString()}`,{headers:{Accept:"application/json, text/plain, */*","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"********"},timeout:3e4})).data;return u.set(e,s),s}finally{u.removePendingRequest(e)}})();return u.setPendingRequest(e,c),c},getAllSalesData:async t=>{const e=[];let o=1;const n=t.maxPages||100;for(;o<=n;){const c=await g.getSalesReport({...t,page:o});if(!c.data||c.data.length===0)break;e.push(...c.data),o++}return e},getSalesSummary:async t=>{const e=await g.getAllSalesData(t),o=e.length,n=e.reduce((i,d)=>i+d.total_amount,0),c=e.reduce((i,d)=>i+d.discount_amount,0),r=e.reduce((i,d)=>i+d.net_amount,0),a=n>0?c/n*100:0,s=e.length>0?e[0].store_name:"";return{storeUid:t.listStoreUid,storeName:s,totalTransactions:o,totalAmount:n,discountAmount:c,netAmount:r,discountPercentage:a,salesData:e}},getDiscountedSales:async t=>g.getSalesReport({...t,isDiscount:1}),getNonDiscountedSales:async t=>g.getSalesReport({...t,isDiscount:0})},m={getSaleNotSyncVat:async t=>{const e=new URLSearchParams({company_uid:t.companyUid,brand_uid:t.brandUid,store_uid:t.storeUid,start_date:t.startDate.toString(),end_date:t.endDate.toString(),page:(t.page||1).toString(),results_per_page:(t.resultsPerPage||50).toString()});return t.sourceId!==void 0&&e.append("source_id",t.sourceId.toString()),(await D.get(`/v3/pos-client/sale-not-sync-vat?${e.toString()}`)).data},getAllSaleNotSyncVat:async t=>{const e=[];let o=1;const n=t.maxPages||100,c=50;for(;o<=n;){const r=await m.getSaleNotSyncVat({companyUid:t.companyUid,brandUid:t.brandUid,storeUid:t.storeUid,startDate:t.startDate,endDate:t.endDate,sourceId:t.sourceId,page:o,resultsPerPage:c});if(!r.data||r.data.length===0)break;e.push(...r.data),o++}return e}},h={getSalesByVoucherCodes:async t=>(await g.getAllSalesData({...t,isDiscount:1})).filter(n=>n.voucher_code&&t.voucherCodes.includes(n.voucher_code)),getVoucherSummary:async t=>{const e=await h.getSalesByVoucherCodes(t),o=e.reduce((a,s)=>a+s.amount_origin,0),n=e.reduce((a,s)=>a+s.discount_amount,0),c=e.reduce((a,s)=>a+s.net_amount,0),r=e.length>0?e[0].store_name:"";return{storeUid:t.listStoreUid,storeName:r,totalAmountOrigin:o,totalDiscountAmount:n,totalNetAmount:c,transactionCount:e.length,voucherSales:e}},getMultiStoreVoucherSummary:async t=>{const e=[];for(let n=0;n<t.storeUids.length;n+=5){const r=t.storeUids.slice(n,n+5).map(async i=>{try{return await h.getVoucherSummary({companyUid:t.companyUid,brandUid:t.brandUid,listStoreUid:i,startDate:t.startDate,endDate:t.endDate,voucherCodes:t.voucherCodes,sourceId:t.sourceId})}catch{return null}}),s=(await Promise.all(r)).filter(i=>i!==null&&i.totalAmountOrigin>0);e.push(...s)}return e},getTotalVoucherAmountOrigin:async t=>{const e=await h.getMultiStoreVoucherSummary(t),o=e.reduce((a,s)=>a+s.totalAmountOrigin,0),n=e.reduce((a,s)=>a+s.totalDiscountAmount,0),c=e.reduce((a,s)=>a+s.totalNetAmount,0),r=e.reduce((a,s)=>a+s.transactionCount,0);return{totalAmountOrigin:o,totalDiscountAmount:n,totalNetAmount:c,totalTransactions:r,storeCount:e.length,storeSummaries:e}}},_={...g,...h,...m,clearCache:u.clear,getCacheStats:u.getStats};export{_ as s};
