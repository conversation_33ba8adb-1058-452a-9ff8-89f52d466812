import{u as g}from"./useQuery-CPo_FvE_.js";import{u as h,a3 as l,a4 as u}from"./index-sntk-7aJ.js";import{u as m}from"./useMutation-jqWRauQa.js";import{a as r}from"./pos-api-CQfNAror.js";import{Q as n}from"./query-keys-3lmd-xp6.js";const i={getComboPromotionsList:async t=>{const e=new URLSearchParams({company_uid:t.company_uid,brand_uid:t.brand_uid,skip_limit:(t.skip_limit??!0).toString(),aggregate:(t.aggregate??!0).toString()});return t.store_uid&&e.append("store_uid",t.store_uid),(await r.get(`/mdata/v1/promotions?${e}`)).data},getCombosList:async t=>{const e=[`company_uid=${t.company_uid}`,`brand_uid=${t.brand_uid}`];if(t.skip_limit?e.push("skip_limit=true"):e.push(`page=${t.page||1}`),t.list_store_uid){const a=Array.isArray(t.list_store_uid)?t.list_store_uid.join(","):t.list_store_uid;e.push(`list_store_uid=${a}`)}t.status&&t.status!=="all"&&e.push(`status=${t.status}`),t.search&&e.push(`search=${encodeURIComponent(t.search)}`),t.promotion_id&&e.push(`promotion_id=${t.promotion_id}`);const o=e.join("&");return(await r.get(`/mdata/v1/packages?${o}`)).data},deleteCombos:async t=>{const e=new URLSearchParams({list_package_uid:t.packageUids.join(",")});await r.delete(`/mdata/v1/packages?${e}`)},updateCombos:async t=>(await r.put("/mdata/v1/packages",t)).data,createCombosFromImport:async t=>(await r.post("/mdata/v1/packages",t)).data,getComboDetails:async t=>{const e=new URLSearchParams({list_package_uid:t.packageUids.join(",")});return(await r.get(`/mdata/v1/package?${e}`)).data},createCombos:async t=>{await r.post("/mdata/v1/packages",t.combos)},createCombo:async t=>{var e,o;try{return(await r.post("/mdata/v1/packages",[t],{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4})).data}catch(s){throw((e=s.response)==null?void 0:e.status)===400?new Error(((o=s.response.data)==null?void 0:o.message)||"Dữ liệu không hợp lệ."):s}},getComboDetail:async t=>{var e;try{return(await r.get(`/mdata/v1/package?list_package_uid=${t}`,{headers:{"accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4})).data}catch(o){throw((e=o.response)==null?void 0:e.status)===404?new Error("Không tìm thấy combo."):o}},updateCombo:async t=>{var e,o;try{return(await r.put("/mdata/v1/packages",[t],{headers:{"Content-Type":"application/json;charset=UTF-8","accept-language":"vi",fabi_type:"pos-cms","x-client-timezone":"25200000"},timeout:3e4})).data}catch(s){throw((e=s.response)==null?void 0:e.status)===400?new Error(((o=s.response.data)==null?void 0:o.message)||"Dữ liệu không hợp lệ."):s}}},v=(t={})=>{const{company:e,brands:o}=h(y=>y.auth),s=o==null?void 0:o[0],{storeUid:a,page:d=1,status:_="all",search:p,promotionUid:c,enabled:C=!0,skipLimit:b=!1}=t;return g({queryKey:[n.COMBOS_LIST,{company_uid:e==null?void 0:e.id,brand_uid:s==null?void 0:s.id,storeUid:a,page:d,status:_,search:p,promotionUid:c,skipLimit:b}],queryFn:async()=>{if(!(e!=null&&e.id)||!(s!=null&&s.id))throw new Error("Company or brand not found");const y={company_uid:e.id,brand_uid:s.id,page:d,status:_,skip_limit:b};return a&&(y.list_store_uid=a),p&&(y.search=p),c&&(y.promotion_id=c),(await i.getCombosList(y)).data},enabled:C&&!!(e!=null&&e.id)&&!!(s!=null&&s.id)})},L=(t={})=>{const{company:e,brands:o}=h(c=>c.auth),s=o==null?void 0:o[0],{enabled:a=!0,storeUid:d,skipLimit:_=!0,aggregate:p=!0}=t;return g({queryKey:[n.COMBO_PROMOTIONS_LIST,{company_uid:e==null?void 0:e.id,brand_uid:s==null?void 0:s.id,storeUid:d,skipLimit:_,aggregate:p}],queryFn:async()=>{if(!(e!=null&&e.id)||!(s!=null&&s.id))throw new Error("Company or brand not found");const c={company_uid:e.id,brand_uid:s.id,skip_limit:_,aggregate:p};return d&&(c.store_uid=d),(await i.getComboPromotionsList(c)).data},enabled:a&&!!(e!=null&&e.id)&&!!(s!=null&&s.id)})},T=()=>{const t=l();return m({mutationFn:e=>i.deleteCombos(e),onSuccess:()=>{t.invalidateQueries({queryKey:[n.COMBOS_LIST]}),u.success("Xóa combo thành công")},onError:e=>{var s,a;const o=((a=(s=e==null?void 0:e.response)==null?void 0:s.data)==null?void 0:a.message)||"Có lỗi xảy ra khi xóa combo";u.error(o)}})},E=()=>m({mutationFn:t=>i.getComboDetails(t),onError:t=>{u.error("Có lỗi xảy ra khi lấy thông tin combo")}}),I=()=>{const t=l();return m({mutationFn:e=>i.createCombos(e),onSuccess:()=>{t.invalidateQueries({queryKey:[n.COMBOS_LIST]}),u.success("Sao chép combo thành công")},onError:e=>{u.error("Có lỗi xảy ra khi sao chép combo")}})};function M(){return{createPromotion:async t=>Promise.resolve()}}function P(){const t=l();return m({mutationFn:async e=>await i.updateCombos(e),onSuccess:()=>{t.invalidateQueries({queryKey:[n.COMBOS_LIST]}),t.invalidateQueries({queryKey:[n.COMBO_PROMOTIONS_LIST]})},onError:e=>{console.error("Error updating combos:",e)}})}function D(){const t=l();return m({mutationFn:async e=>await i.createCombo(e),onSuccess:()=>{t.invalidateQueries({queryKey:[n.COMBOS_LIST]}),u.success("Tạo combo thành công!")},onError:e=>{var s,a;const o=((a=(s=e==null?void 0:e.response)==null?void 0:s.data)==null?void 0:a.message)||(e==null?void 0:e.message)||"Có lỗi xảy ra khi tạo combo";u.error(o)}})}function F(t,e=!0){const o=Array.isArray(t)?t:[t];return g({queryKey:[n.COMBO_DETAIL,o],queryFn:async()=>o.length===0?[]:(await i.getComboDetail(o.join(","))).data,enabled:e&&o.length>0,staleTime:5*60*1e3,gcTime:10*60*1e3})}function U(){const t=l();return m({mutationFn:async e=>Array.isArray(e)?await i.updateCombos(e):await i.updateCombo(e),onSuccess:()=>{t.invalidateQueries({queryKey:[n.COMBOS_LIST]}),t.invalidateQueries({queryKey:[n.COMBO_DETAIL]}),u.success("Cập nhật combo thành công!")},onError:e=>{var s,a;const o=((a=(s=e==null?void 0:e.response)==null?void 0:s.data)==null?void 0:a.message)||(e==null?void 0:e.message)||"Có lỗi xảy ra khi cập nhật combo";u.error(o)}})}function K(t){return{data:null,isLoading:!1,error:null}}function Q(t,e={}){const o=l();return m({mutationFn:async s=>Promise.resolve(),onSuccess:()=>{var s;o.invalidateQueries({queryKey:[n.COMBOS_LIST]}),(s=e.onSuccess)==null||s.call(e)},onError:s=>{var a;(a=e.onError)==null||a.call(e,s)}})}function $(){return{validateFormData:t=>[]}}export{T as a,P as b,i as c,v as d,E as e,I as f,D as g,U as h,F as i,K as j,Q as k,$ as l,M as m,L as u};
