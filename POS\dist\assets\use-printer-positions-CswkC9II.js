import{a3 as s,a4 as r}from"./index-sntk-7aJ.js";import{u as n}from"./useMutation-jqWRauQa.js";import{Q as o}from"./query-keys-3lmd-xp6.js";import"./pos-api-CQfNAror.js";const m=()=>{const t=s();return n({mutationFn:async e=>({success:!0}),onSuccess:()=>{t.invalidateQueries({queryKey:[o.PRINTER_POSITIONS]}),r.success("Cập nhật vị trí máy in thành công")},onError:e=>{r.error("Có lỗi xảy ra khi cập nhật vị trí máy in")}})};export{m as u};
