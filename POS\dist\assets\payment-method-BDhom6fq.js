import{r as L,a as Bt,R as Et,j as t,u as Ot,B as Vt,c as kt}from"./index-sntk-7aJ.js";import{D as le}from"./date-range-picker-CBqQlAZr.js";import"./form-2rWd-Izg.js";import{D as de,a as me,b as he,c as bt}from"./dropdown-menu-Cq_TXEG8.js";import"./user-B67nntxu.js";import{k as ue}from"./date-utils-DBbLjCz0.js";import{d as Lt}from"./excel-util-ipSMJz6Z.js";import{utils as $}from"./xlsx-DkH2s96g.js";import"./crm-api-DhFa_vPG.js";import{a as pe}from"./pos-api-CQfNAror.js";import{u as zt,g as ge,a as fe}from"./use-payment-method-revenue-CJ9sBNCl.js";import{p as _e}from"./payment-methods-api-BMP84Pfb.js";import"./vietqr-api-C-lVxzD-.js";import{u as xe}from"./use-pos-data-CRVu7vxu.js";import{u as Kt}from"./use-pos-cities-data-eA_u6z_U.js";import{C as Ut}from"./combobox-B4y2_kzV.js";import{B as Pt}from"./badge-BulEkXWC.js";import{C as At}from"./checkbox-gkM78Twn.js";import{C as Xt,a as qt,b as Yt}from"./collapsible-DwXQUepC.js";import{P as Ht,a as Gt,b as Jt}from"./popover-DUo0D-5L.js";import{C as Dt}from"./select-ZzLBlgJd.js";import{D as jt}from"./download-DIRnyul3.js";import{C as Rt,d as It}from"./card-CfUruATe.js";import{R as Qt,X as Zt,Y as te,B as ee,H as ne}from"./generateCategoricalChart-DOYdoRWE.js";import{L as oe}from"./LineChart-CMFOOFNK.js";import{L as se}from"./Line-BuISMy97.js";import{f as $t}from"./isSameMonth-C8JQo-AN.js";import{T as ye,a as je,b as Tt,c as lt,d as Se,e as at}from"./table-BEvnnqp-.js";import"./calendar-BhUTNdpd.js";import"./createLucideIcon-CvoWT756.js";import"./index-CyrU-3zB.js";import"./chevron-right-JsGDE6eB.js";import"./react-icons.esm-BTYMKzFL.js";import"./index-S3x6QFUG.js";import"./index-DBwfoC6_.js";import"./check-Dykbakem.js";import"./useQuery-CPo_FvE_.js";import"./utils-km2FGkQ4.js";import"./query-keys-3lmd-xp6.js";import"./use-auth-BDL_Eyjj.js";import"./useMutation-jqWRauQa.js";import"./command-Ct8kkkRi.js";import"./dialog-HHE8oSxh.js";import"./search-DFsa4jPY.js";import"./chevrons-up-down-BwW14lXu.js";import"./index-2BmiXKhT.js";import"./index-Chjiymov.js";const Ne={getSaleByDate:async e=>{const o=new URLSearchParams({company_uid:e.company_uid,brand_uid:e.brand_uid,list_store_uid:e.list_store_uid,start_date:e.start_date.toString(),end_date:e.end_date.toString()});return e.csv!==void 0&&o.append("csv",e.csv.toString()),e.store_open_at!==void 0&&o.append("store_open_at",e.store_open_at.toString()),e.results_per_page!==void 0&&o.append("results_per_page",e.results_per_page.toString()),e.page!==void 0&&o.append("page",e.page.toString()),(await pe.get(`/reports_v1/v3/pos-cms/report/sale-by-date?${o.toString()}`)).data}};function we(e){const o=new Date(e),c=o.getDate().toString().padStart(2,"0"),x=(o.getMonth()+1).toString(),S=o.getFullYear().toString().slice(-2);return`${c}/${x}/${S}`}function vt(e){const o=new Date(e),c=o.getDate().toString().padStart(2,"0"),x=(o.getMonth()+1).toString(),S=o.getFullYear().toString().slice(-2);return`${c}/${x}/${S}`}function be(e,o,c,x){const S=vt(o),k=vt(c),C=[],d=`Bảng kê chi tiết hóa đơn bán hàng kèm PTTT từ ${S} 00:00 đến ${k} 23:59 tại cửa hàng ${x}`;C.push([d]);const m=new Set;e.forEach(h=>{h.sale_payment_method.forEach(j=>{m.add(j.payment_method_name)})});const f=Array.from(m).sort(),y=["Ngày","Tran Id","Tran No","Bàn","Tổng tiền trước giảm giá","Tiền giảm giá","Tiền chiết khấu","Tiền VAT","Tổng tiền",...f,"Khách hàng"];C.push(y);const s={totalBeforeDiscount:0,discountAmount:0,additionalDiscount:0,vatAmount:0,finalTotal:0,paymentMethods:{}};f.forEach(h=>{s.paymentMethods[h]=0}),e.forEach(h=>{const j=h.amount_origin,z=h.amount_discount_detail+h.amount_discount_price,Y=h.discount_extra_amount,gt=h.vat_amount,T=h.total_amount;s.totalBeforeDiscount+=j,s.discountAmount+=z,s.additionalDiscount+=Y,s.vatAmount+=gt,s.finalTotal+=T;const B={};f.forEach(Z=>{B[Z]=0}),h.sale_payment_method.forEach(Z=>{const P=Z.payment_method_name,rt=Z.amount;B.hasOwnProperty(P)&&(B[P]+=rt,s.paymentMethods[P]+=rt)});const F=[vt(h.tran_date),h.tran_id,h.tran_no,h.table_name||"",j,z,Y,gt,T,...f.map(Z=>B[Z]),""];C.push(F)});const N=["Tổng","","","",s.totalBeforeDiscount,s.discountAmount,s.additionalDiscount,s.vatAmount,s.finalTotal,...f.map(h=>s.paymentMethods[h]),""];C.push(N);const _=$.aoa_to_sheet(C),l=[{wch:12},{wch:20},{wch:15},{wch:10},{wch:20},{wch:15},{wch:15},{wch:12},{wch:15},...f.map(()=>({wch:15})),{wch:15}];_["!cols"]=l,_["!merges"]||(_["!merges"]=[]);const w=$.encode_range({s:{c:0,r:0},e:{c:y.length-1,r:0}});return _["!merges"].push($.decode_range(w)),_}function Te(e,o,c,x){const S=$.book_new(),k=vt(o),C=vt(c),d=[],m=`Bảng kê chi tiết kèm phương thức thanh toán từ ${k} 00:00 đến ${C} 23:59`;d.push([m]);const f=new Set;e.forEach(T=>{Object.keys(T.payment_methods).forEach(B=>f.add(B))});const y=Array.from(f).sort(),s=["Ngày","Tên điểm","Tổng tiền trước giảm giá","Tiền giảm giá","Tiền chiết khấu","Voucher","Tổng tiền sau GG + CK",...y];d.push(s);const N=new Map;e.forEach(T=>{N.has(T.date)||N.set(T.date,[]),N.get(T.date).push(T)}),Array.from(N.keys()).sort((T,B)=>{const F=new Date(T.split("/").reverse().join("-")).getTime(),Z=new Date(B.split("/").reverse().join("-")).getTime();return F-Z}).forEach(T=>{const B=N.get(T),F={total_before_discount:0,discount_amount:0,additional_discount:0,voucher_amount:0,total_after_discount:0,payment_methods:{}};y.forEach(P=>{F.payment_methods[P]=0}),B.forEach(P=>{F.total_before_discount+=P.total_before_discount,F.discount_amount+=P.discount_amount,F.additional_discount+=P.additional_discount,F.voucher_amount+=P.voucher_amount,F.total_after_discount+=P.total_after_discount,y.forEach(rt=>{F.payment_methods[rt]+=P.payment_methods[rt]||0})});const Z=[T,"",F.total_before_discount,F.discount_amount,F.additional_discount,F.voucher_amount,F.total_after_discount];y.forEach(P=>{Z.push(F.payment_methods[P])}),d.push(Z),B.forEach(P=>{const rt=[P.fb_store_id,P.store_name,P.total_before_discount,P.discount_amount,P.additional_discount,P.voucher_amount,P.total_after_discount];y.forEach(ht=>{rt.push(P.payment_methods[ht]||0)}),d.push(rt)})});const l={total_before_discount:0,discount_amount:0,additional_discount:0,voucher_amount:0,total_after_discount:0,payment_methods:{}};y.forEach(T=>{l.payment_methods[T]=0}),e.forEach(T=>{l.total_before_discount+=T.total_before_discount,l.discount_amount+=T.discount_amount,l.additional_discount+=T.additional_discount,l.voucher_amount+=T.voucher_amount,l.total_after_discount+=T.total_after_discount,y.forEach(B=>{l.payment_methods[B]+=T.payment_methods[B]||0})});const w=["","Tổng",l.total_before_discount,l.discount_amount,l.additional_discount,l.voucher_amount,l.total_after_discount];y.forEach(T=>{w.push(l.payment_methods[T])}),d.push(w);const h=$.aoa_to_sheet(d),j=[{wch:12},{wch:25},{wch:18},{wch:15},{wch:15},{wch:12},{wch:18},...Array(y.length).fill({wch:15})];h["!cols"]=j,h["!merges"]||(h["!merges"]=[]);const z=$.encode_range({s:{c:0,r:0},e:{c:s.length-1,r:0}});h["!merges"].push($.decode_range(z)),$.book_append_sheet(S,h,"BCOLC"),Lt(S,"bcolc_report")}const ae=L.createContext(void 0),ve=({children:e})=>{var Wt;const[o,c]=L.useState(()=>{const v=new Date;return{from:v,to:v}}),[x,S]=L.useState(0),[k,C]=L.useState([]),[d,m]=L.useState([]),[f,y]=L.useState([]),[s,N]=L.useState([]),{company:_,activeBrands:l}=xe(),w=(_==null?void 0:_.id)||"",h=((Wt=l[0])==null?void 0:Wt.id)||"",{currentBrandStores:j}=Bt(),z=j.map(v=>v.id||v.store_id),Y=(v,g,A)=>{const b=[];return v.forEach(ot=>{A.filter(U=>U.city_uid===ot).forEach(U=>{const ct=U.id||U.store_id;ct&&!b.includes(ct)&&b.push(ct)})}),g.forEach(ot=>{A.find(U=>(U.id||U.store_id)===ot)&&!b.includes(ot)&&b.push(ot)}),b},gt=Y(k,d,j),T=gt.length>0?gt:z,[B,F]=L.useMemo(()=>ue([o.from,o.to]),[o.from,o.to,x]),Z=()=>{S(v=>v+1)},{data:P,isLoading:rt}=zt({startDate:B,endDate:F,selectedStoreIds:T,companyUid:w,brandUid:h,enabled:!0}),ht=Y(f,s,j),p=ht.length>0,{data:n,isLoading:i}=zt({startDate:B,endDate:F,selectedStoreIds:ht,companyUid:w,brandUid:h,enabled:p}),r=Et.useMemo(()=>{if(T.length===z.length)return"Tất cả cửa hàng";const v=T.map(g=>{const A=j.find(b=>(b.id||b.store_id)===g);return(A==null?void 0:A.store_name)||""}).filter(g=>g);return v.length<=2?v.join(", "):`${v.slice(0,2).join(", ")} và ${v.length-2} cửa hàng khác`},[T,z,j]),a=Et.useMemo(()=>{if(ht.length===0)return"";const v=ht.map(g=>{const A=j.find(b=>(b.id||b.store_id)===g);return(A==null?void 0:A.store_name)||""}).filter(g=>g);return v.length<=2?v.join(", "):`${v.slice(0,2).join(", ")} và ${v.length-2} cửa hàng khác`},[ht,j]),u=(v,g,A)=>{if(v.length===0)return null;const b=new Map,ot=new Set;v.forEach(I=>{I.storeData.data.forEach(R=>{ot.add(R.payment_method_name),R.list_data.forEach(O=>{const it=O.date;b.has(it)||b.set(it,{});const mt=b.get(it)[R.payment_method_name]||{count:0,amount:0};b.set(it,{...b.get(it),[R.payment_method_name]:{count:mt.count+O.total_count,amount:mt.amount+O.total_amount}})})})});const tt=Array.from(ot),U=[];U.push([`Báo cáo PTTT từ ${g} 00:00 đến ${A} 23:59 tại tất cả cửa hàng`]);const ct=["PTTT"];tt.forEach(I=>ct.push(I,"")),ct.push("Tổng",""),U.push(ct);const V=["Ngày"];tt.forEach(()=>{V.push("Tổng PTTT","Tổng tiền")}),V.push("Tổng PTTT","Tổng tiền"),U.push(V);const st=Array.from(b.keys()).sort();st.forEach(I=>{const R=new Date(I),O=R.getDate().toString().padStart(2,"0"),it=(R.getMonth()+1).toString().padStart(2,"0"),mt=R.getFullYear(),G=[`${O}/${it}/${mt}`],nt=b.get(I);tt.forEach(ft=>{const pt=nt[ft];pt?G.push(pt.count.toString(),pt.amount.toString()):G.push("0","0")});const xt=tt.reduce((ft,pt)=>{const _t=nt[pt];return ft+(_t?_t.count:0)},0),Mt=tt.reduce((ft,pt)=>{const _t=nt[pt];return ft+(_t?_t.amount:0)},0);G.push(xt.toString(),Mt.toString()),U.push(G)});const W=["Tổng"];let Q=0,J=0;tt.forEach(I=>{let R=0,O=0;st.forEach(it=>{const q=b.get(it)[I];q&&(R+=q.count,O+=q.amount)}),W.push(R.toString(),O.toString()),Q+=R,J+=O}),W.push(Q.toString(),J.toString()),U.push(W);const E=$.aoa_to_sheet(U);E["!merges"]||(E["!merges"]=[]);const X=tt.length*2+2,dt=$.encode_range({s:{c:0,r:0},e:{c:X,r:0}});E["!merges"].push($.decode_range(dt));for(let I=0;I<tt.length;I++){const R=1+I*2,O=R+1,it=$.encode_range({s:{c:R,r:1},e:{c:O,r:1}});E["!merges"].push($.decode_range(it))}const et=$.encode_range({s:{c:X-1,r:1},e:{c:X,r:1}});return E["!merges"].push($.decode_range(et)),E.A2={v:"PTTT",s:{alignment:{horizontal:"center",vertical:"center"}}},E},K=async v=>{try{const g=$.book_new(),A=new Date(B).toLocaleDateString("vi-VN"),b=new Date(F).toLocaleDateString("vi-VN"),ot=v.filter(V=>V.id||V.store_id).map(async V=>{const st=V.id||V.store_id;try{const Q=await fe({brand_uid:h,company_uid:w,start_date:B,end_date:F,list_store_uid:st,store_open_at:0,by_days:1});if(!Q.data||Q.data.length===0)return null;const J=`${V.fb_store_id}_${V.store_name}`,E=Q.data.map(q=>q.payment_method_name),X=[];X.push([`Báo cáo PTTT từ ${A} 00:00 đến ${b} 23:59 tại cửa hàng ${V.store_name}`]);const dt=["PTTT"];E.forEach(q=>dt.push(q,"")),dt.push("Tổng",""),X.push(dt);const et=["Ngày"];E.forEach(()=>{et.push("Tổng PTTT","Tổng tiền")}),et.push("Tổng PTTT","Tổng tiền"),X.push(et);const I=new Map;Q.data.forEach(q=>{q.list_data.forEach(G=>{const nt=G.date;I.has(nt)||I.set(nt,{}),I.set(nt,{...I.get(nt),[q.payment_method_name]:{count:G.total_count,amount:G.total_amount}})})});const R=Array.from(I.keys()).sort();R.forEach(q=>{const G=new Date(q),nt=G.getDate().toString().padStart(2,"0"),xt=(G.getMonth()+1).toString().padStart(2,"0"),Mt=G.getFullYear(),pt=[`${nt}/${xt}/${Mt}`],_t=I.get(q);E.forEach(Nt=>{const yt=_t[Nt];yt?pt.push(yt.count.toString(),yt.amount.toString()):pt.push("0","0")});const ce=E.reduce((Nt,yt)=>{const wt=_t[yt];return Nt+(wt?wt.count:0)},0),ie=E.reduce((Nt,yt)=>{const wt=_t[yt];return Nt+(wt?wt.amount:0)},0);pt.push(ce.toString(),ie.toString()),X.push(pt)});const O=["Tổng"];let it=0,mt=0;return E.forEach(q=>{let G=0,nt=0;R.forEach(xt=>{const ft=I.get(xt)[q];ft&&(G+=ft.count,nt+=ft.amount)}),O.push(G.toString(),nt.toString()),it+=G,mt+=nt}),O.push(it.toString(),mt.toString()),X.push(O),{sheetName:J,rows:X,paymentMethodsCount:E.length,storeData:Q}}catch(W){return console.error(`Error processing store ${V.store_name}:`,W),null}}),U=(await Promise.all(ot)).filter(V=>V!==null);U.forEach(V=>{const st=$.aoa_to_sheet(V.rows);st["!merges"]||(st["!merges"]=[]);const W=V.paymentMethodsCount*2+2,Q=$.encode_range({s:{c:0,r:0},e:{c:W,r:0}});st["!merges"].push($.decode_range(Q));for(let E=0;E<V.paymentMethodsCount;E++){const X=1+E*2,dt=X+1,et=$.encode_range({s:{c:X,r:1},e:{c:dt,r:1}});st["!merges"].push($.decode_range(et))}const J=$.encode_range({s:{c:W-1,r:1},e:{c:W,r:1}});st["!merges"].push($.decode_range(J)),st.A2={v:"PTTT",s:{alignment:{horizontal:"center",vertical:"center"}}},$.book_append_sheet(g,st,V.sheetName)});const ct=u(U,A,b);ct&&$.book_append_sheet(g,ct,"Tất cả cửa hàng"),Lt(g,"payment_methods_report")}catch(g){console.error("Export error:",g)}},H=()=>{if(d.length===0)K(j);else{const v=Y(k,d,j).map(g=>j.find(A=>(A.id||A.store_id)===g)).filter(g=>g!==void 0);K(v)}},M=async()=>{K(j)},D=async v=>{try{if(v.length===0){console.warn("No stores provided for export");return}const{company:g,brands:A}=Ot.getState().auth,b=A==null?void 0:A[0];if(!(g!=null&&g.id)||!(b!=null&&b.id)){console.warn("Company or brand not found");return}const ot=o.from.getTime(),tt=o.to.getTime()+86399999,U=$.book_new(),ct=v.map(async W=>{const Q=W.id||W.store_id||"",J=W.store_name||"Unknown Store";if(!Q)return console.warn(`Store UID not found for store: ${J}`),null;try{const E=[];let X=1,dt=!0;for(;dt;){const et={company_uid:g.id,brand_uid:b.id,list_store_uid:Q,start_date:ot,end_date:tt,csv:1,store_open_at:0,results_per_page:200,page:X},I=await Ne.getSaleByDate(et);I.data&&I.data.length>0?(E.push(...I.data),X++):dt=!1}if(E.length>0){const et=E.sort((R,O)=>R.tran_date!==O.tran_date?R.tran_date-O.tran_date:R.tran_no.localeCompare(O.tran_no));return{sheetName:`${W.fb_store_id}-${W.store_name}`,salesData:et,storeName:J}}return null}catch(E){return console.error(`Error processing store ${J}:`,E),null}}),st=(await Promise.all(ct)).filter(W=>W!==null);if(st.length===0){console.warn("No sales data found for any selected stores");return}st.forEach(W=>{const Q=be(W.salesData,ot,tt,W.storeName);$.book_append_sheet(U,Q,W.sheetName)}),Lt(U,"detailed_payment_methods_report")}catch(g){console.error("Error exporting detailed sales report:",g)}},ut=()=>{if(d.length===0&&D(j),d.length>0){const v=Y(k,d,j).map(g=>j.find(A=>(A.id||A.store_id)===g)).filter(g=>g!==void 0);D(v)}},St=()=>{D(j)},Ft=async v=>{try{const{company:g,brands:A}=Ot.getState().auth,b=A==null?void 0:A[0],ot=o.from.getTime(),tt=o.to.getTime()+86399999,ct=(await _e.getPaymentMethods({company_uid:(g==null?void 0:g.id)||"",brand_uid:(b==null?void 0:b.id)||"",skip_limit:!0,is_fb:0})).data||[],V=v.map(async J=>{const E=J.id||J.store_id||"",X=J.store_name||"",dt=String(J.fb_store_id||"");try{const et=await ge({company_uid:(g==null?void 0:g.id)||"",brand_uid:(b==null?void 0:b.id)||"",list_store_uid:E,start_date:ot,end_date:tt,store_open_at:0,by_days:1});if(et.data&&et.data.length>0){const I=[];return et.data.forEach(R=>{const O=R.tran_date||Date.now(),mt={date:we(O),timestamp:O,store_name:X,fb_store_id:dt,total_before_discount:R.revenue_gross,discount_amount:0,additional_discount:R.discount_extra_amount,voucher_amount:R.voucher_amount,total_after_discount:R.revenue_net,payment_methods:{}};ct.forEach(q=>{mt.payment_methods[q.payment_method_name]=0}),R.payment_methods.forEach(q=>{var nt;const G=(nt=ct.find(xt=>xt.payment_method_id===q.payment_method_id))==null?void 0:nt.payment_method_name;G&&mt.payment_methods.hasOwnProperty(G)&&(mt.payment_methods[G]=q.total_amount)}),I.push(mt)}),I}}catch(et){console.error(`Error processing store ${X}:`,et)}return[]}),Q=(await Promise.all(V)).flat().sort((J,E)=>{if(J.timestamp!==E.timestamp)return J.timestamp-E.timestamp;const X=parseInt(J.fb_store_id)||0,dt=parseInt(E.fb_store_id)||0;return X-dt});Q.length>0&&Te(Q,ot,tt)}catch(g){console.error("Error exporting payment method details:",g)}},re={dateRange:o,setDateRange:c,selectedCities:k,selectedStores:d,setSelectedCities:C,setSelectedStores:m,comparisonCities:f,comparisonStores:s,setComparisonCities:y,setComparisonStores:N,data:P,isLoading:rt,comparisonData:n||[],isComparisonLoading:i,selectedStoreNames:r,comparisonStoreNames:a,handleUpdateDateRange:Z,handleExportSelectedStores:H,handleExportAllStores:M,handleExportDetailedSelectedStores:ut,handleExportDetailedAllStores:St,handleExportDetailedWithPaymentMethod:()=>{Ft(j)}};return t.jsx(ae.Provider,{value:re,children:e})},Ct=()=>{const e=L.useContext(ae);if(e===void 0)throw new Error("usePaymentMethod must be used within a PaymentMethodProvider");return e},De=({selectedCities:e,selectedStores:o,comparisonCities:c,comparisonStores:x,onComparisonCitiesChange:S,onComparisonStoresChange:k,className:C})=>{const[d,m]=L.useState(!1),[f,y]=L.useState(new Set),[s,N]=L.useState("all"),[_,l]=L.useState(""),{cities:w}=Kt(),{currentBrandStores:h}=Bt(),j=L.useMemo(()=>[...e.flatMap(n=>h.filter(r=>r.city_uid===n).map(r=>r.id||r.store_id)),...o],[e,o,h]),z=p=>h.filter(n=>n.city_uid===p).filter(n=>{const i=n.id||n.store_id;return!j.includes(i)}).filter(n=>s==="all"?!0:s==="chain"?n.is_franchise===0:n.is_franchise===1),Y=p=>{const n=new Set(f);n.has(p)?n.delete(p):n.add(p),y(n)},gt=(p,n)=>{if(n)S([...c,p]);else{S(c.filter(a=>a!==p));const r=z(p).map(a=>a.id||a.store_id);k(x.filter(a=>!r.includes(a)))}},T=(p,n)=>{const i=h.find(a=>(a.id||a.store_id)===p),r=i==null?void 0:i.city_uid;if(n){const a=[...x,p];if(k(a),r){const u=z(r),K=u.map(M=>M.id||M.store_id);if(K.every(M=>a.includes(M)||c.includes(r))&&u.length>0){const M=a.filter(D=>!K.includes(D));k(M),S([...c,r])}}}else if(r&&c.includes(r)){const K=z(r).map(H=>H.id||H.store_id).filter(H=>H!==p);S(c.filter(H=>H!==r)),k([...x,...K])}else k(x.filter(a=>a!==p))},B=p=>c.includes(p),F=p=>{const i=z(p).map(a=>a.id||a.store_id),r=x.filter(a=>i.includes(a));return r.length>0&&r.length<i.length},Z=p=>x.includes(p),P=L.useMemo(()=>{const p=c.reduce((i,r)=>{const a=z(r);return i+a.length},0),n=x.filter(i=>{const r=h.find(u=>(u.id||u.store_id)===i);if(!r)return!0;const a=r.city_uid;return!c.includes(a)}).length;return p+n},[c,x,h,z]),rt=P>0?`Đã chọn ${P} cửa hàng`:"Chọn cửa hàng so sánh",ht=L.useMemo(()=>{const p=_.trim().toLowerCase();return w.some(n=>{const i=n.id||n.city_id,r=z(i);return p?r.some(a=>String(a.store_name||"").toLowerCase().includes(p)):r.length>0})},[w,h,s,_,j]);return t.jsxs(Ht,{open:d,onOpenChange:m,children:[t.jsx(Gt,{asChild:!0,children:t.jsxs(Vt,{variant:"outline",role:"combobox","aria-expanded":d,className:kt("w-[320px] justify-between",C),children:[t.jsx("span",{className:"truncate",children:rt}),t.jsx(Dt,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),t.jsx(Jt,{className:"w-80 p-0",align:"start",children:t.jsx("div",{className:"max-h-96 overflow-y-auto",children:t.jsxs("div",{className:"p-2",children:[t.jsx("div",{className:"mb-2 flex flex-col items-start justify-center gap-2",children:t.jsx(Ut,{options:[{value:"all",label:"Tất cả cửa hàng"},{value:"chain",label:"Chuỗi"},{value:"franchise",label:"Nhượng quyền"}],value:s,onValueChange:p=>N(p||"all"),placeholder:"Loại cửa hàng",searchPlaceholder:"Tìm kiếm...",className:"w-full"})}),!ht&&t.jsx("div",{className:"text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold",children:"Không có dữ liệu"}),ht&&w.filter(p=>{const n=p.id||p.city_id,i=z(n),r=_.trim().toLowerCase();return r?i.some(a=>String(a.store_name||"").toLowerCase().includes(r)):i.length>0}).map(p=>{const n=p.id||p.city_id,i=_.trim().toLowerCase(),r=z(n),a=i?r.filter(M=>String(M.store_name||"").toLowerCase().includes(i)):r,u=i?!0:f.has(n),K=B(n),H=F(n);return t.jsx("div",{className:"mb-2",children:t.jsxs(Xt,{open:u,onOpenChange:()=>Y(n),children:[t.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[t.jsx(At,{checked:K,ref:M=>{if(M&&H&&!K){const D=M.querySelector('input[type="checkbox"]');D&&(D.indeterminate=!0)}},onCheckedChange:M=>gt(n,M)}),t.jsxs(qt,{className:"flex flex-1 items-center space-x-2 text-left",children:[t.jsx("span",{className:"font-medium",children:p.city_name}),t.jsxs("span",{className:"text-muted-foreground text-sm",children:["(",a.length," cửa hàng)"]}),t.jsx(Dt,{className:kt("ml-auto h-4 w-4 transition-transform",u&&"rotate-180 transform")})]})]}),t.jsx(Yt,{children:u&&t.jsx("div",{className:"ml-6 space-y-1",children:a.map(M=>{const D=M.id||M.store_id,ut=Z(D)||K;return t.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[t.jsx(At,{checked:ut,onCheckedChange:St=>T(D,St)}),t.jsx("span",{className:"text-sm",children:M.store_name}),typeof M.is_franchise<"u"&&(M.is_franchise===1?t.jsx(Pt,{className:"ml-2",children:"Nhượng quyền"}):t.jsx(Pt,{className:"ml-2",children:"Chuỗi"}))]},D)})})})]})},n)})]})})})]})},Ce=({selectedCities:e,selectedStores:o,onCitiesChange:c,onStoresChange:x,className:S,excludeCities:k=[],excludeStores:C=[]})=>{const[d,m]=L.useState(!1),[f,y]=L.useState(new Set),[s,N]=L.useState("all"),[_,l]=L.useState(""),{cities:w}=Kt(),{currentBrandStores:h}=Bt(),j=L.useMemo(()=>w.filter(n=>!k.includes(n.id)),[w,k]),z=L.useMemo(()=>h.filter(n=>!C.includes(n.id||n.store_id)).filter(n=>s==="all"?!0:s==="chain"?n.is_franchise===0:n.is_franchise===1),[h,C,s]),Y=n=>z.filter(i=>i.city_uid===n),gt=n=>{const i=new Set(f);i.has(n)?i.delete(n):i.add(n),y(i)},T=(n,i)=>{if(i)c([...e,n]);else{c(e.filter(u=>u!==n));const a=Y(n).map(u=>u.id||u.store_id);x(o.filter(u=>!a.includes(u)))}},B=(n,i)=>{const r=h.find(u=>(u.id||u.store_id)===n),a=r==null?void 0:r.city_uid;if(i){const u=[...o,n];if(x(u),a){const H=Y(a).map(D=>D.id||D.store_id);if(H.every(D=>u.includes(D)||e.includes(a))){const D=u.filter(ut=>!H.includes(ut));x(D),c([...e,a])}}}else if(a&&e.includes(a)){const H=Y(a).map(M=>M.id||M.store_id).filter(M=>M!==n);c(e.filter(M=>M!==a)),x([...o,...H])}else x(o.filter(u=>u!==n))},F=n=>e.includes(n),Z=n=>{const r=Y(n).map(u=>u.id||u.store_id),a=o.filter(u=>r.includes(u));return a.length>0&&a.length<r.length},P=n=>o.includes(n),rt=L.useMemo(()=>{const n=e.reduce((r,a)=>{const u=Y(a);return r+u.length},0),i=o.filter(r=>{const a=h.find(K=>(K.id||K.store_id)===r);if(!a)return!0;const u=a.city_uid;return!e.includes(u)}).length;return n+i},[e,o,h,Y]),ht=rt>0?`Đã chọn ${rt} cửa hàng`:"Chọn cửa hàng",p=L.useMemo(()=>{const n=_.trim().toLowerCase();return j.some(i=>{const r=i.id||i.city_id,a=Y(r);return n?a.some(u=>String(u.store_name||"").toLowerCase().includes(n)):a.length>0})},[j,z,_]);return t.jsxs(Ht,{open:d,onOpenChange:m,children:[t.jsx(Gt,{asChild:!0,children:t.jsxs(Vt,{variant:"outline",role:"combobox","aria-expanded":d,className:kt("w-[320px] justify-between",S),children:[t.jsx("span",{className:"truncate",children:ht}),t.jsx(Dt,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),t.jsx(Jt,{className:"w-80 p-0",align:"start",children:t.jsx("div",{className:"max-h-96 overflow-y-auto",children:t.jsxs("div",{className:"p-2",children:[t.jsx("div",{className:"mb-2 flex flex-col items-start justify-center gap-2",children:t.jsx(Ut,{options:[{value:"all",label:"Tất cả cửa hàng"},{value:"chain",label:"Chuỗi"},{value:"franchise",label:"Nhượng quyền"}],value:s,onValueChange:n=>N(n||"all"),placeholder:"Loại cửa hàng",searchPlaceholder:"Tìm kiếm...",className:"w-full"})}),!p&&t.jsx("div",{className:"text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold",children:"Không có dữ liệu"}),p&&j.filter(n=>{const i=n.id||n.city_id,r=Y(i),a=_.trim().toLowerCase();return a?r.some(u=>String(u.store_name||"").toLowerCase().includes(a)):r.length>0}).map(n=>{const i=n.id||n.city_id,r=_.trim().toLowerCase(),a=Y(i),u=r?a.filter(D=>String(D.store_name||"").toLowerCase().includes(r)):a,K=r?!0:f.has(i),H=F(i),M=Z(i);return t.jsx("div",{className:"mb-2",children:t.jsxs(Xt,{open:K,onOpenChange:()=>gt(i),children:[t.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[t.jsx(At,{checked:H,ref:D=>{if(D&&M&&!H){const ut=D.querySelector('input[type="checkbox"]');ut&&(ut.indeterminate=!0)}},onCheckedChange:D=>T(i,D)}),t.jsxs(qt,{className:"flex flex-1 items-center space-x-2 text-left",children:[t.jsx("span",{className:"font-medium",children:n.city_name}),t.jsxs("span",{className:"text-muted-foreground text-sm",children:["(",u.length," cửa hàng)"]}),t.jsx(Dt,{className:kt("ml-auto h-4 w-4 transition-transform",K&&"rotate-180 transform")})]})]}),t.jsx(Yt,{children:K&&t.jsx("div",{className:"ml-6 space-y-1",children:u.map(D=>{const ut=D.id||D.store_id,St=P(ut)||H;return t.jsxs("div",{className:"flex items-center space-x-2 rounded p-2 hover:bg-gray-50",children:[t.jsx(At,{checked:St,onCheckedChange:Ft=>B(ut,Ft)}),t.jsx("span",{className:"text-sm",children:D.store_name}),typeof D.is_franchise<"u"&&(D.is_franchise===1?t.jsx(Pt,{className:"ml-2",children:"Nhượng quyền"}):t.jsx(Pt,{className:"ml-2",children:"Chuỗi"}))]},ut)})})})]})},i)})]})})})]})},Me=()=>{const{dateRange:e,setDateRange:o,selectedCities:c,selectedStores:x,setSelectedCities:S,setSelectedStores:k,comparisonCities:C,comparisonStores:d,setComparisonCities:m,setComparisonStores:f,handleUpdateDateRange:y,handleExportSelectedStores:s,handleExportDetailedSelectedStores:N,handleExportDetailedAllStores:_,handleExportDetailedWithPaymentMethod:l,handleExportAllStores:w}=Ct(),h=c.length>0||x.length>0;return t.jsxs("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between",children:[t.jsxs("div",{className:"flex flex-col items-center gap-4 md:flex-row md:gap-6",children:[t.jsx(le,{initialDateFrom:e.from,initialDateTo:e.to,onUpdate:({range:j})=>{j!=null&&j.from&&(j!=null&&j.to)&&(o({from:j.from,to:j.to}),y())},align:"start",locale:"vi-VN"}),t.jsx(Ce,{selectedCities:c,selectedStores:x,onCitiesChange:S,onStoresChange:k,className:"w-[200px]"}),h&&t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("span",{className:"text-sm font-medium text-gray-600",children:"so với"}),t.jsx(De,{selectedCities:c,selectedStores:x,comparisonCities:C,comparisonStores:d,onComparisonCitiesChange:m,onComparisonStoresChange:f,className:"w-[200px]"})]})]}),t.jsxs(de,{children:[t.jsx(me,{asChild:!0,children:t.jsxs(Vt,{variant:"outline",size:"sm",children:[t.jsx(jt,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo",t.jsx(Dt,{className:"ml-2 h-4 w-4"})]})}),t.jsxs(he,{align:"end",className:"w-80",children:[t.jsxs(bt,{onClick:s,children:[t.jsx(jt,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo theo cửa hàng đã chọn"]}),t.jsxs(bt,{onClick:()=>w(),children:[t.jsx(jt,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo tất cả cửa hàng"]}),t.jsxs(bt,{onClick:N,children:[t.jsx(jt,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo chi tiết cửa hàng đã chọn"]}),t.jsxs(bt,{onClick:_,children:[t.jsx(jt,{className:"mr-2 h-4 w-4"}),"Xuất báo cáo chi tiết tất cả cửa hàng"]}),t.jsxs(bt,{onClick:l,children:[t.jsx(jt,{className:"mr-2 h-4 w-4"}),"Xuất bảng kê chi tiết kèm PTTT"]})]})]})]})},Ee=e=>e>=1e6?Math.round(e/1e6)+"M":e>=1e3?Math.round(e/1e3)+"K":new Intl.NumberFormat("vi-VN").format(e),ke=({active:e,payload:o,label:c})=>{var x,S;if(e&&o&&o.length&&c){const k=((S=(x=o[0])==null?void 0:x.payload)==null?void 0:S.details)||{},C=Object.entries(k).filter(([d,m])=>m&&m.total_amount>0);if(C.length>0)return t.jsxs("div",{className:"rounded-lg border bg-white p-3 shadow-lg",style:{zIndex:999},children:[t.jsxs("p",{className:"mb-2 font-semibold text-gray-900",children:["Ngày ",c]}),C.map(([d,m])=>t.jsxs("div",{className:"mb-2 last:mb-0",children:[t.jsxs("p",{className:"font-medium text-gray-800",children:[d,":"]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Tổng tiền: ",new Intl.NumberFormat("vi-VN").format(m.total_amount),"đ"]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Tổng hóa đơn: ",m.total_bill]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Phí cà thẻ: ",new Intl.NumberFormat("vi-VN").format(m.payment_fee_amount),"đ"]})]},d))]})}return null},Pe=({payload:e})=>e&&e.length?t.jsx("div",{className:"mt-4 flex flex-wrap items-center justify-center gap-4",children:e.map((o,c)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"h-3 w-3 rounded-full",style:{backgroundColor:o.color}}),t.jsx("span",{className:"text-sm text-gray-700",children:o.value})]},c))}):null,Ae=()=>{const{data:e,isLoading:o}=Ct(),{chartData:c,paymentMethods:x}=L.useMemo(()=>{if(e.length===0)return{chartData:[{name:"01/01"},{name:"01/01"}],paymentMethods:[]};const S=new Set;e.forEach(m=>{m.list_data.forEach(f=>{const y=$t(new Date(f.date),"dd/MM");S.add(y)})});const C=Array.from(S).sort((m,f)=>{const[y,s]=m.split("/").map(Number),[N,_]=f.split("/").map(Number);return s-_||y-N}).map(m=>{const f={name:m,details:{}};return e.forEach(y=>{const s=y.list_data.find(N=>$t(new Date(N.date),"dd/MM")===m);s?(f[y.payment_method_name]=s.total_amount,f.details[y.payment_method_name]={total_amount:s.total_amount,total_bill:s.total_bill,payment_fee_amount:s.payment_fee_amount}):(f[y.payment_method_name]=0,f.details[y.payment_method_name]={total_amount:0,total_bill:0,payment_fee_amount:0})}),f}),d=e.map(m=>({name:m.payment_method_name,id:m.payment_method_id}));return{chartData:C,paymentMethods:d}},[e]);return o?t.jsx(Rt,{children:t.jsx(It,{children:t.jsx("div",{className:"flex h-64 items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu..."})})})}):t.jsx(Rt,{className:"z-10",children:t.jsx(It,{children:t.jsx("div",{className:"h-64",children:t.jsx(Qt,{width:"100%",height:"100%",children:t.jsxs(oe,{data:c,margin:{top:20,right:30,left:20,bottom:20},children:[t.jsx(Zt,{dataKey:"name",tick:{fontSize:10},interval:0,angle:0,textAnchor:"middle",height:40}),t.jsx(te,{domain:e.length===0?[0,5]:[0,"dataMax + 1000000"],ticks:e.length===0?[0,1,2,3,4,5]:void 0,tick:{fontSize:12},tickFormatter:Ee}),t.jsx(ee,{content:t.jsx(ke,{}),isAnimationActive:!1,wrapperStyle:{zIndex:999}}),t.jsx(ne,{content:t.jsx(Pe,{}),wrapperStyle:{paddingTop:"20px"}}),x.map((S,k)=>{const C=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#06b6d4"],d=C[k%C.length];return t.jsx(se,{type:"linear",dataKey:S.name,stroke:d,strokeWidth:2,dot:!1,activeDot:!1},S.id)})]})})})})})},Re=()=>{const{data:e,isLoading:o,comparisonData:c,comparisonCities:x,comparisonStores:S,selectedStoreNames:k,comparisonStoreNames:C}=Ct(),d=(x.length>0||S.length>0)&&c.length>0,m=s=>new Intl.NumberFormat("vi-VN").format(s),f=Et.useMemo(()=>{if(e.length===0)return null;const s=e.reduce((l,w)=>l+w.total_bill,0),N=e.reduce((l,w)=>l+w.total_payment_fee_amount,0),_=e.reduce((l,w)=>l+w.total_amount,0);return{totalBill:s,totalFees:N,grandTotal:_}},[e]),y=Et.useMemo(()=>{if(!d)return null;const s=c.reduce((l,w)=>l+w.total_bill,0),N=c.reduce((l,w)=>l+w.total_payment_fee_amount,0),_=c.reduce((l,w)=>l+w.total_amount,0);return{totalBill:s,totalFees:N,grandTotal:_}},[c,d]);return o?t.jsx("div",{children:t.jsx("div",{className:"flex h-64 items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu..."})})}):t.jsx("div",{children:t.jsxs("div",{className:"rounded-md border",children:[t.jsxs(ye,{children:[t.jsx(je,{children:d?t.jsxs(t.Fragment,{children:[t.jsxs(Tt,{children:[t.jsx(lt,{className:"w-12 border-r",rowSpan:2,children:"#"}),t.jsx(lt,{className:"border-r",rowSpan:2,children:"PTTT"}),t.jsx(lt,{className:"border-r border-b text-center",colSpan:3,children:k}),t.jsx(lt,{className:"border-b text-center",colSpan:3,children:C})]}),t.jsxs(Tt,{children:[t.jsx(lt,{className:"text-right",children:"Tổng PTTT"}),t.jsx(lt,{className:"text-right",children:"Phí cà thẻ"}),t.jsx(lt,{className:"border-r text-right",children:"Tổng tiền"}),t.jsx(lt,{className:"text-right",children:"Tổng PTTT"}),t.jsx(lt,{className:"text-right",children:"Phí cà thẻ"}),t.jsx(lt,{className:"text-right",children:"Tổng tiền"})]})]}):t.jsxs(Tt,{children:[t.jsx(lt,{className:"w-12",children:"#"}),t.jsx(lt,{children:"PTTT"}),t.jsx(lt,{className:"text-right",children:"Tổng PTTT"}),t.jsx(lt,{className:"text-right",children:"Phí cà thẻ"}),t.jsx(lt,{className:"text-right",children:"Tổng tiền"})]})}),e.length>0&&t.jsxs(Se,{children:[e.map((s,N)=>{const _=s.total_amount-s.total_payment_fee_amount,l=d?c.find(h=>h.payment_method_id===s.payment_method_id):null,w=l?l.total_amount-l.total_payment_fee_amount:0;return t.jsxs(Tt,{children:[t.jsx(at,{className:"font-medium",children:N+1}),t.jsx(at,{className:"font-medium",children:s.payment_method_name}),t.jsx(at,{className:"text-right font-mono",children:s.total_bill}),t.jsxs(at,{className:"text-right font-mono",children:[m(s.total_payment_fee_amount)," đ"]}),t.jsxs(at,{className:"text-right font-mono font-semibold",children:[m(_)," đ"]}),d&&t.jsxs(t.Fragment,{children:[t.jsx(at,{className:"text-right font-mono",children:l?l.total_bill:0}),t.jsxs(at,{className:"text-right font-mono",children:[l?m(l.total_payment_fee_amount):"0"," đ"]}),t.jsxs(at,{className:"text-right font-mono font-semibold",children:[l?m(w):"0"," đ"]})]})]},s.payment_method_id)}),f&&t.jsxs(Tt,{className:"border-t-2 border-gray-300 bg-gray-50 font-semibold",children:[t.jsx(at,{className:"font-bold"}),t.jsx(at,{className:"font-bold",children:"Tổng"}),t.jsx(at,{className:"text-right font-mono font-bold",children:f.totalBill}),t.jsxs(at,{className:"text-right font-mono font-bold",children:[m(f.totalFees)," đ"]}),t.jsxs(at,{className:"text-right font-mono font-bold",children:[m(f.grandTotal)," đ"]}),d&&y&&t.jsxs(t.Fragment,{children:[t.jsx(at,{className:"text-right font-mono font-bold",children:y.totalBill}),t.jsxs(at,{className:"text-right font-mono font-bold",children:[m(y.totalFees)," đ"]}),t.jsxs(at,{className:"text-right font-mono font-bold",children:[m(y.grandTotal)," đ"]})]})]})]})]}),e.length===0&&t.jsx("div",{className:"text-muted-foreground flex h-32 items-center justify-center",children:"Không có dữ liệu phương thức thanh toán"})]})})},Ie=e=>e>=1e6?Math.round(e/1e6)+"M":e>=1e3?Math.round(e/1e3)+"K":new Intl.NumberFormat("vi-VN").format(e),$e=({active:e,payload:o,label:c})=>{var x,S;if(e&&o&&o.length&&c){const k=((S=(x=o[0])==null?void 0:x.payload)==null?void 0:S.details)||{},C=Object.entries(k).filter(([d,m])=>m&&m.total_amount>0);if(C.length>0)return t.jsxs("div",{className:"rounded-lg border bg-white p-3 shadow-lg",style:{zIndex:999},children:[t.jsxs("p",{className:"mb-2 font-semibold text-gray-900",children:["Ngày ",c]}),C.map(([d,m])=>t.jsxs("div",{className:"mb-2 last:mb-0",children:[t.jsxs("p",{className:"font-medium text-gray-800",children:[d,":"]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Tổng tiền: ",new Intl.NumberFormat("vi-VN").format(m.total_amount),"đ"]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Tổng hóa đơn: ",m.total_bill]}),t.jsxs("p",{className:"text-sm text-gray-600",children:["Phí cà thẻ: ",new Intl.NumberFormat("vi-VN").format(m.payment_fee_amount),"đ"]})]},d))]})}return null},Fe=({payload:e})=>e&&e.length?t.jsx("div",{className:"mt-4 flex flex-wrap items-center justify-center gap-4",children:e.map((o,c)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"h-3 w-3 rounded-full",style:{backgroundColor:o.color}}),t.jsx("span",{className:"text-sm text-gray-700",children:o.value})]},c))}):null,Le=()=>{const{comparisonData:e,isComparisonLoading:o,comparisonCities:c,comparisonStores:x}=Ct(),S=c.length>0||x.length>0,{chartData:k,paymentMethods:C}=L.useMemo(()=>{if(e.length===0)return{chartData:[{name:"01/01"},{name:"01/01"}],paymentMethods:[]};const d=new Set;e.forEach(s=>{s.list_data.forEach(N=>{const _=$t(new Date(N.date),"dd/MM");d.add(_)})});const f=Array.from(d).sort((s,N)=>{const[_,l]=s.split("/").map(Number),[w,h]=N.split("/").map(Number);return l-h||_-w}).map(s=>{const N={name:s,details:{}};return e.forEach(_=>{const l=_.list_data.find(w=>$t(new Date(w.date),"dd/MM")===s);l?(N[_.payment_method_name]=l.total_amount,N.details[_.payment_method_name]={total_amount:l.total_amount,total_bill:l.total_bill,payment_fee_amount:l.payment_fee_amount}):(N[_.payment_method_name]=0,N.details[_.payment_method_name]={total_amount:0,total_bill:0,payment_fee_amount:0})}),N}),y=e.map(s=>({name:s.payment_method_name,id:s.payment_method_id}));return{chartData:f,paymentMethods:y}},[e]);return S?o?t.jsx(Rt,{className:"z-10",children:t.jsx(It,{children:t.jsx("div",{className:"flex h-64 items-center justify-center",children:t.jsx("div",{className:"text-muted-foreground",children:"Đang tải dữ liệu so sánh..."})})})}):t.jsx(Rt,{className:"z-10",children:t.jsx(It,{children:t.jsx("div",{className:"h-64",children:t.jsx(Qt,{width:"100%",height:"100%",children:t.jsxs(oe,{data:k,margin:{top:20,right:30,left:20,bottom:20},children:[t.jsx(Zt,{dataKey:"name",tick:{fontSize:10},interval:0,angle:0,textAnchor:"middle",height:40}),t.jsx(te,{domain:e.length===0?[0,5]:[0,"dataMax + 1000000"],ticks:e.length===0?[0,1,2,3,4,5]:void 0,tick:{fontSize:12},tickFormatter:Ie}),t.jsx(ee,{content:t.jsx($e,{}),isAnimationActive:!1}),t.jsx(ne,{content:t.jsx(Fe,{}),wrapperStyle:{paddingTop:"20px"}}),C.map((d,m)=>{const f=["#f59e0b","#8b5cf6","#06b6d4","#ef4444","#10b981","#3b82f6"],y=f[m%f.length];return t.jsx(se,{type:"linear",dataKey:d.name,stroke:y,strokeWidth:2,dot:!1,activeDot:!1},d.id)})]})})})})}):null},Be=()=>{const{comparisonCities:e,comparisonStores:o}=Ct(),c=e.length>0||o.length>0;return t.jsxs("div",{className:"space-y-6 p-4",children:[t.jsx(Me,{}),t.jsxs("div",{className:c?"grid grid-cols-1 gap-6 lg:grid-cols-2":"w-full",children:[t.jsx(Ae,{}),c&&t.jsx(Le,{})]}),t.jsx(Re,{})]})},Ve=()=>t.jsx(ve,{children:t.jsx(Be,{})}),$n=Ve;export{$n as component};
