var D=i=>{throw TypeError(i)};var x=(i,e,s)=>e.has(i)||D("Cannot "+s);var n=(i,e,s)=>(x(i,e,"read from private field"),s?s.call(i):e.get(i)),b=(i,e,s)=>e.has(i)?D("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(i):e.set(i,s),c=(i,e,s,t)=>(x(i,e,"write to private field"),t?t.call(i,s):e.set(i,s),s),y=(i,e,s)=>(x(i,e,"access private method"),s);import{a6 as K,aa as k,b1 as N,a3 as V,r as w}from"./index-sntk-7aJ.js";import{Q as G,a as X,b as Y,e as Z,c as $,d as W,s as F,f as T,w as ee,g as se}from"./useQuery-CPo_FvE_.js";import{n as te}from"./utils-km2FGkQ4.js";function _(i,e){return i.filter(s=>!e.includes(s))}function re(i,e,s){const t=i.slice(0);return t[e]=s,t}var g,f,v,Q,p,R,C,S,M,a,P,B,H,L,A,j,ie=(j=class extends K{constructor(e,s,t){super();b(this,a);b(this,g);b(this,f);b(this,v);b(this,Q);b(this,p);b(this,R);b(this,C);b(this,S);b(this,M,[]);c(this,g,e),c(this,Q,t),c(this,v,[]),c(this,p,[]),c(this,f,[]),this.setQueries(s)}onSubscribe(){this.listeners.size===1&&n(this,p).forEach(e=>{e.subscribe(s=>{y(this,a,L).call(this,e,s)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,n(this,p).forEach(e=>{e.destroy()})}setQueries(e,s){c(this,v,e),c(this,Q,s),k.batch(()=>{const t=n(this,p),u=y(this,a,H).call(this,n(this,v));c(this,M,u),u.forEach(o=>o.observer.setOptions(o.defaultedQueryOptions));const r=u.map(o=>o.observer),h=r.map(o=>o.getCurrentResult()),d=r.some((o,E)=>o!==t[E]);t.length===r.length&&!d||(c(this,p,r),c(this,f,h),this.hasListeners()&&(_(t,r).forEach(o=>{o.destroy()}),_(r,t).forEach(o=>{o.subscribe(E=>{y(this,a,L).call(this,o,E)})}),y(this,a,A).call(this)))})}getCurrentResult(){return n(this,f)}getQueries(){return n(this,p).map(e=>e.getCurrentQuery())}getObservers(){return n(this,p)}getOptimisticResult(e,s){const t=y(this,a,H).call(this,e),u=t.map(r=>r.observer.getOptimisticResult(r.defaultedQueryOptions));return[u,r=>y(this,a,B).call(this,r??u,s),()=>y(this,a,P).call(this,u,t)]}},g=new WeakMap,f=new WeakMap,v=new WeakMap,Q=new WeakMap,p=new WeakMap,R=new WeakMap,C=new WeakMap,S=new WeakMap,M=new WeakMap,a=new WeakSet,P=function(e,s){return s.map((t,u)=>{const r=e[u];return t.defaultedQueryOptions.notifyOnChangeProps?r:t.observer.trackResult(r,h=>{s.forEach(d=>{d.observer.trackProp(h)})})})},B=function(e,s){return s?((!n(this,R)||n(this,f)!==n(this,S)||s!==n(this,C))&&(c(this,C,s),c(this,S,n(this,f)),c(this,R,N(n(this,R),s(e)))),n(this,R)):e},H=function(e){const s=new Map(n(this,p).map(u=>[u.options.queryHash,u])),t=[];return e.forEach(u=>{const r=n(this,g).defaultQueryOptions(u),h=s.get(r.queryHash);h?t.push({defaultedQueryOptions:r,observer:h}):t.push({defaultedQueryOptions:r,observer:new G(n(this,g),r)})}),t},L=function(e,s){const t=n(this,p).indexOf(e);t!==-1&&(c(this,f,re(n(this,f),t,s)),y(this,a,A).call(this))},A=function(){var e;if(this.hasListeners()){const s=n(this,R),t=y(this,a,P).call(this,n(this,f),n(this,M)),u=y(this,a,B).call(this,t,(e=n(this,Q))==null?void 0:e.combine);s!==u&&k.batch(()=>{this.listeners.forEach(r=>{r(n(this,f))})})}},j);function ce({queries:i,...e},s){const t=V(),u=X(),r=Y(),h=w.useMemo(()=>i.map(l=>{const m=t.defaultQueryOptions(l);return m._optimisticResults=u?"isRestoring":"optimistic",m}),[i,t,u]);h.forEach(l=>{Z(l),$(l,r)}),W(r);const[d]=w.useState(()=>new ie(t,h,e)),[o,E,J]=d.getOptimisticResult(h,e.combine),z=!u&&e.subscribed!==!1;w.useSyncExternalStore(w.useCallback(l=>z?d.subscribe(k.batchCalls(l)):te,[d,z]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),w.useEffect(()=>{d.setQueries(h,e)},[h,e,d]);const I=o.some((l,m)=>F(h[m],l))?o.flatMap((l,m)=>{const O=h[m];if(O){const U=new G(t,O);if(F(O,l))return T(O,U,r);ee(l,u)&&T(O,U,r)}return[]}):[];if(I.length>0)throw Promise.all(I);const q=o.find((l,m)=>{const O=h[m];return O&&se({result:l,errorResetBoundary:r,throwOnError:O.throwOnError,query:t.getQueryCache().get(O.queryHash),suspense:O.suspense})});if(q!=null&&q.error)throw q.error;return E(J())}export{ce as u};
