import{r as y,u as ne,a3 as Ge,a4 as F,h as Ae,j as e,B as I,c as Me,T as qe,o as $e,p as We,q as Qe,R as Oe,l as Xe}from"./index-sntk-7aJ.js";import{b as Je}from"./pos-api-CQfNAror.js";import"./vietqr-api-C-lVxzD-.js";import{u as Ye}from"./use-customizations-Dvjv5zwl.js";import"./user-B67nntxu.js";import"./crm-api-DhFa_vPG.js";import{H as Ze}from"./header-CePLmjHC.js";import{M as et}from"./main-BTno3738.js";import{P as tt}from"./profile-dropdown-CteBDQaM.js";import{S as st,T as at}from"./search-BAjrNsV6.js";import{u as fe,a as it,b as nt,c as lt,g as Pe,d as rt,e as ct,f as ot,h as dt,I as mt,i as ht,j as ut,k as xt,C as pt,B as ft}from"./customization-dialog-DO7pArCV.js";import{E as gt}from"./exceljs.min-C2daZjFR.js";import{h as _t,x as Ce,G as ke,H as yt,J as jt,K as wt,i as bt,A as Nt,a as vt,C as Ct,B as kt}from"./react-icons.esm-BTYMKzFL.js";import{D as St,a as It,b as Tt,c as Q}from"./dropdown-menu-Cq_TXEG8.js";import{D as G}from"./data-table-column-header-DeuI7A-H.js";import{B as Be}from"./badge-BulEkXWC.js";import{S as Dt}from"./status-badge-zDeSSiEZ.js";import"./date-range-picker-CBqQlAZr.js";import"./form-2rWd-Izg.js";import{C as Re}from"./checkbox-gkM78Twn.js";import{S as de}from"./settings-D6TN9hKu.js";import{I as Ft}from"./IconCopy-C1FNK1-Z.js";import{I as Et}from"./IconTrash-BHnBYU-I.js";import{u as ze,g as Mt,a as Ot,b as Pt,d as Bt,e as Le,f as pe}from"./index-C8dGDLCq.js";import{S as ge,a as $}from"./scroll-area-CPF3eFT1.js";import{T as _e,a as ye,b as q,c as ae,d as je,e as W}from"./table-BEvnnqp-.js";import{C as He}from"./confirm-dialog-CKbdgUwC.js";import{a as Rt,C as Vt}from"./chevron-right-JsGDE6eB.js";import{i as At,u as le}from"./use-item-types-DlwgINfg.js";import{u as re}from"./use-removed-items-DUNjx3Y6.js";import{I as zt}from"./input-DCw8aMl6.js";import{S as me,a as he,b as ue,c as xe,d as X}from"./select-ZzLBlgJd.js";import{M as Lt}from"./multi-select-SpNKBlxk.js";import{T as Se}from"./trash-2-FzW9PTsH.js";import{I as Ht}from"./IconFilter-0izWfqLQ.js";import{X as Kt}from"./calendar-BhUTNdpd.js";import{S as N}from"./skeleton-BrdgPuty.js";import{u as Ut}from"./use-cities-CoxXlYjl.js";import{read as Ie,utils as Te}from"./xlsx-DkH2s96g.js";import{u as we}from"./use-item-classes-DVU3n-Lf.js";import{u as be}from"./use-units-DSbsiywC.js";import{D as J,a as Y,b as Z,c as ee}from"./dialog-HHE8oSxh.js";import{C as ve}from"./combobox-B4y2_kzV.js";import{u as De}from"./useQuery-CPo_FvE_.js";import{u as Gt}from"./useMutation-jqWRauQa.js";import{i as qt}from"./item-api-CFTFT5cc.js";import{s as $t}from"./sources-api-CATymHLl.js";import{Q as ie}from"./query-keys-3lmd-xp6.js";import{D as Wt}from"./download-DIRnyul3.js";import{U as Qt}from"./upload-C8ni-I8t.js";import"./separator-BIRyiZJ0.js";import"./avatar-CcvCHous.js";import"./search-context-iBulh32Z.js";import"./command-Ct8kkkRi.js";import"./search-DFsa4jPY.js";import"./createLucideIcon-CvoWT756.js";import"./createReactComponent-BcntBX1O.js";import"./IconChevronRight-CuIdFTCw.js";import"./IconSearch-BFRP7UUl.js";import"./use-dialog-state-Ck6p_fTK.js";import"./modal-Yuq_-Dyf.js";import"./zod-DMJBVHr6.js";import"./index-S3x6QFUG.js";import"./index-CyrU-3zB.js";import"./index-DBwfoC6_.js";import"./check-Dykbakem.js";import"./popover-DUo0D-5L.js";import"./isSameMonth-C8JQo-AN.js";import"./index-2BmiXKhT.js";import"./alert-dialog-QV_IGTCu.js";import"./circle-x-C-gqcnnq.js";import"./chevrons-up-down-BwW14lXu.js";import"./utils-km2FGkQ4.js";import"./sources-CfiQ7039.js";const Fe=[{sourceId:"10000045",name:"ZALO"},{sourceId:"10000049",name:"FACEBOOK"},{sourceId:"10000134",name:"SO"},{sourceId:"10000162",name:"CRM"},{sourceId:"10000165",name:"VNPAY"},{sourceId:"10000168",name:"GOJEK (GOVIET)"},{sourceId:"10000169",name:"ShopeeFood"},{sourceId:"10000171",name:"MANG VỀ"},{sourceId:"10000172",name:"TẠI CHỖ"},{sourceId:"10000176",name:"CALL CENTER"},{sourceId:"10000216",name:"O2O"},{sourceId:"10000253",name:"BEFOOD"}],Xt=()=>{const l=new gt.Workbook;return l.creator="POS System",l.lastModifiedBy="POS System",l.created=new Date,l.modified=new Date,l},Jt=()=>{const l=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)"],t=Fe.map(s=>`${s.name} [${s.sourceId}]`);return[...l,...t]},Yt=(l,t)=>{const i=l.map(o=>{const h=t.find(f=>f.id===o.item_type_uid),c=(h==null?void 0:h.item_type_name)||"Uncategory";return{item:o,itemTypeName:c}}).reduce((o,{item:h,itemTypeName:c})=>(o[c]||(o[c]=[]),o[c].push({item:h,itemTypeName:c}),o),{}),a=Object.keys(i).sort(),r=[];return a.forEach(o=>{const h=i[o];h.sort((c,f)=>c.item.item_name.localeCompare(f.item.item_name)),h.forEach(({item:c,itemTypeName:f})=>{const j=[c.id,c.item_id,c.item_name,f,c.ots_price||0,(c.ots_tax||0)*100];Fe.forEach(S=>{var _,w;const v=(w=(_=c.extra_data)==null?void 0:_.price_by_source)==null?void 0:w.find(x=>x.source_id===S.sourceId);j.push((v==null?void 0:v.price)||"")}),r.push(j)})}),r},Zt=(l,t,s)=>{const i=l.addWorksheet("Sheet"),a=Jt(),r=Yt(t,s);return i.addRow(a).eachCell(c=>{c.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FF0560A6"}},c.font={color:{argb:"FFFFFFFF"},bold:!0,size:11},c.alignment={horizontal:"center",vertical:"middle"},c.border={top:{style:"thin",color:{argb:"FF000000"}},left:{style:"thin",color:{argb:"FF000000"}},bottom:{style:"thin",color:{argb:"FF000000"}},right:{style:"thin",color:{argb:"FF000000"}}}}),r.forEach(c=>{i.addRow(c).eachCell(j=>{j.font={size:10},j.border={top:{style:"thin",color:{argb:"FFD0D0D0"}},left:{style:"thin",color:{argb:"FFD0D0D0"}},bottom:{style:"thin",color:{argb:"FFD0D0D0"}},right:{style:"thin",color:{argb:"FFD0D0D0"}}}})}),[40,15,25,15,12,10,...Fe.map(()=>15)].forEach((c,f)=>{i.getColumn(f+1).width=c}),i},es=async(l,t,s)=>{try{const i=Xt();Zt(i,l,t);const a=await i.xlsx.writeBuffer(),r=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),o=new Date().toISOString().slice(0,19).replace(/:/g,"-"),h=`import-price-by-source_${s}_${o}.xlsx`,c=window.URL.createObjectURL(r),f=document.createElement("a");return f.href=c,f.download=h,document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(c),Promise.resolve()}catch(i){return console.error("Error creating price by source Excel file:",i),Promise.reject(i)}},ts=["item_uid","item_id","item_name","Nhóm món","Giá gốc","Vat (%)","ZALO [10000045]","FACEBOOK [10000049]","SO [10000134]","CRM [10000162]","VNPAY [10000165]","GOJEK (GOVIET) [10000168]","ShopeeFood [10000169]","MANG VỀ [10000171]","TẠI CHỖ [10000172]","CALL CENTER [10000176]","O2O [10000216]","BEFOOD [10000253]"],ss=async l=>new Promise((t,s)=>{const i=new FileReader;i.onload=a=>{var r;try{const o=(r=a.target)==null?void 0:r.result;if(!o){s(new Error("Không thể đọc file"));return}const h=Ie(o,{type:"array"}),c=h.SheetNames[0];if(!c){s(new Error("File Excel không có sheet nào"));return}const f=h.Sheets[c],j=Te.sheet_to_json(f,{header:1});if(j.length<2){s(new Error("File Excel không có dữ liệu"));return}const S=j[0],v=ts.filter(w=>!S.includes(w));v.length>0&&console.warn("Missing headers:",v);const _=[];for(let w=1;w<j.length;w++){const x=j[w];if(!x||x.length===0)continue;const g={item_uid:"",item_id:"",item_name:"",item_type_name:"",ots_price:0,ots_tax:0};S.forEach((T,P)=>{const p=x[P];switch(T){case"item_uid":g.item_uid=String(p||"");break;case"item_id":g.item_id=String(p||"");break;case"item_name":g.item_name=String(p||"");break;case"Nhóm món":g.item_type_name=String(p||"");break;case"Giá gốc":g.ots_price=parseFloat(String(p||"0"))||0;break;case"Vat (%)":g.ots_tax=parseFloat(String(p||"0"))||0;break;case"ZALO [10000045]":case"FACEBOOK [10000049]":case"SO [10000134]":case"CRM [10000162]":case"VNPAY [10000165]":case"GOJEK (GOVIET) [10000168]":case"ShopeeFood [10000169]":case"MANG VỀ [10000171]":case"TẠI CHỖ [10000172]":case"CALL CENTER [10000176]":case"O2O [10000216]":case"BEFOOD [10000253]":if(p!=null&&p!==""){const n=parseFloat(String(p));isNaN(n)||(g[T]=n)}break;default:p!=null&&(g[T]=p);break}}),g.item_uid&&g.item_id&&g.item_name&&_.push(g)}console.log("📊 Parsed Excel data:",{totalRows:j.length-1,validItems:_.length,headers:S,sampleItem:_[0]}),t(_)}catch(o){console.error("Error parsing Excel file:",o),s(new Error("Có lỗi xảy ra khi đọc file Excel"))}},i.onerror=()=>{s(new Error("Có lỗi xảy ra khi đọc file"))},i.readAsArrayBuffer(l)}),as=l=>{if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel","application/octet-stream"].includes(l.type)&&!l.name.match(/\.(xlsx|xls)$/i))return{isValid:!1,error:"File phải có định dạng Excel (.xlsx hoặc .xls)"};const s=10*1024*1024;return l.size>s?{isValid:!1,error:"File không được vượt quá 10MB"}:{isValid:!0}};function is(){const[l,t]=y.useState(!1),[s,i]=y.useState(!1),[a,r]=y.useState(null),[o,h]=y.useState(!1),[c,f]=y.useState([]),[j,S]=y.useState("all"),[v,_]=y.useState("all"),[w,x]=y.useState([]),[g,T]=y.useState("all");return{isCustomizationDialogOpen:l,isBuffetItem:s,selectedMenuItem:a,isBuffetConfigModalOpen:o,selectedBuffetMenuItem:c,selectedItemTypeUid:j,selectedCityUid:v,selectedDaysOfWeek:w,selectedStatus:g,setIsCustomizationDialogOpen:t,setIsBuffetItem:i,setSelectedMenuItem:r,setIsBuffetConfigModalOpen:h,setSelectedBuffetMenuItem:f,setSelectedItemTypeUid:S,setSelectedCityUid:_,setSelectedDaysOfWeek:x,setSelectedStatus:T}}const ns=(l,t=!0)=>{const{company:s,brands:i}=ne(r=>r.auth),a=i==null?void 0:i[0];return De({queryKey:[ie.ITEMS_LIST,"price-by-source",l],queryFn:async()=>{const r={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:l,skip_limit:!0,active:1};return(await qt.getItems(r)).data||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&l),staleTime:5*60*1e3,gcTime:10*60*1e3})},ls=(l,t=!0)=>{const{company:s,brands:i}=ne(r=>r.auth),a=i==null?void 0:i[0];return De({queryKey:[ie.SOURCES,"price-by-source",l],queryFn:async()=>{const r={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:l,skip_limit:!0};return await $t.getSources(r)||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&l),staleTime:5*60*1e3,gcTime:10*60*1e3})},rs=(l,t=!0)=>{const{company:s,brands:i}=ne(r=>r.auth),a=i==null?void 0:i[0];return De({queryKey:[ie.ITEM_TYPES,"price-by-source",l],queryFn:async()=>{const r={company_uid:(s==null?void 0:s.id)||"",brand_uid:(a==null?void 0:a.id)||"",city_uid:l,skip_limit:!0,active:1};return(await At.getItemTypes(r)).data||[]},enabled:t&&!!(s!=null&&s.id&&(a!=null&&a.id)&&l),staleTime:5*60*1e3,gcTime:10*60*1e3})},cs=(l,t=!0)=>{const s=ns(l,t),i=ls(l,t),a=rs(l,t);return{items:s.data||[],sources:i.data||[],itemTypes:a.data||[],isLoading:s.isLoading||i.isLoading||a.isLoading,isError:s.isError||i.isError||a.isError,error:s.error||i.error||a.error,refetch:()=>{s.refetch(),i.refetch(),a.refetch()}}},os=(l,t,s)=>l.map(i=>{const a=t.find(h=>h.id===i.item_uid);if(!a)throw new Error(`Original item not found for ${i.item_uid}`);const r=[],o={};return s.forEach(h=>{const c=`${h.sourceName} [${h.sourceId}]`;o[c]=h.sourceId}),Object.entries(o).forEach(([h,c])=>{const f=i[h];if(f!=null&&f!==""){const j=typeof f=="string"?parseFloat(f):f;!isNaN(j)&&j>0&&r.push({source_id:c,price:j})}}),{...a,ots_price:i.ots_price,ots_tax:i.ots_tax/100,ta_price:i.ots_price,ta_tax:i.ots_tax/100,extra_data:{...a.extra_data,price_by_source:r}}}),ds=()=>{const l=Ge(),{mutate:t,isPending:s}=Gt({mutationFn:async i=>{const a=os(i.previewItems,i.originalItems,i.sources),r=await Je.put("/mdata/v1/items",a);return r.data.data||r.data},onSuccess:()=>{l.invalidateQueries({queryKey:[ie.ITEMS_LIST],refetchType:"none"}),setTimeout(()=>{l.refetchQueries({queryKey:[ie.ITEMS_LIST]})},100),F.success("Đã cập nhật cấu hình giá theo nguồn thành công!")},onError:i=>{F.error(`Có lỗi xảy ra khi lưu cấu hình: ${i.message}`)}});return{bulkUpdatePriceBySource:t,isUpdating:s}};function ms(){const{setOpen:l}=fe(),t=Ae(),s=()=>{t({to:"/menu/items/items-in-city/detail"})},i=()=>{l("export-dialog")},a=()=>{l("import")},r=()=>{l("price-by-source-config")},o=()=>{},h=()=>{},c=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(St,{children:[e.jsx(It,{asChild:!0,children:e.jsxs(I,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(_t,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(Tt,{align:"end",className:"w-56",children:[e.jsxs(Q,{onClick:i,children:[e.jsx(Ce,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs(Q,{onClick:a,children:[e.jsx(ke,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs(Q,{onClick:r,children:[e.jsx(yt,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs(Q,{onClick:o,children:[e.jsx(jt,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs(Q,{onClick:h,children:[e.jsx(wt,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs(Q,{onClick:c,children:[e.jsx(bt,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(I,{variant:"default",size:"sm",onClick:s,children:"Tạo món"})]})})}function Ve({column:l,title:t,className:s,defaultSort:i="desc"}){if(!l.getCanSort())return e.jsx("div",{className:Me(s),children:t});const a=()=>{const r=l.getIsSorted();r?r==="desc"?l.toggleSorting(!1):l.toggleSorting(!0):l.toggleSorting(i==="desc")};return e.jsx("div",{className:Me("flex items-center space-x-2",s),children:e.jsxs(I,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:t}),l.getIsSorted()==="desc"?e.jsx(Nt,{className:"ml-2 h-4 w-4"}):l.getIsSorted()==="asc"?e.jsx(vt,{className:"ml-2 h-4 w-4"}):e.jsx(Ct,{className:"ml-2 h-4 w-4"})]})})}const hs=({onBuffetConfigClick:l})=>[{id:"select",header:({table:t})=>e.jsx(Re,{checked:t.getIsAllPageRowsSelected(),onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(Re,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),"aria-label":"Select row",onClick:s=>s.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:t})=>e.jsx("div",{className:"w-[50px]",children:t.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:t})=>e.jsx(G,{column:t,title:"Mã món"}),cell:({row:t})=>e.jsx("div",{className:"text-sm font-medium",children:t.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:t})=>e.jsx(G,{column:t,title:"Tên món"}),cell:({row:t})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:t.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:t})=>e.jsx(G,{column:t,title:"Giá"}),cell:({row:t})=>{const s=t.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(s)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:t})=>e.jsx(G,{column:t,title:"VAT (%)"}),cell:({row:t})=>{const s=t.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:s*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:t})=>e.jsx(G,{column:t,title:"Nhóm món"}),cell:({row:t})=>e.jsx(Be,{variant:"outline",className:"text-xs",children:t.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:t})=>e.jsx(G,{column:t,title:"Loại món"}),cell:({row:t})=>t.getValue("itemClass")&&e.jsx(Be,{variant:"outline",className:"text-center text-xs",children:t.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:t})=>e.jsx(G,{column:t,title:"Đơn vị tính"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:t})=>e.jsx(Ve,{column:t,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:t})=>{const s=t.getValue("sideItems");if(!s)return e.jsx("div",{children:"Món chính"});const i=s==="Món ăn kèm"?"Món ăn kèm":s;return e.jsx(qe,{children:e.jsxs($e,{children:[e.jsx(We,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:i})}),e.jsx(Qe,{children:e.jsx("p",{className:"max-w-[300px]",children:i})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:t})=>e.jsx(G,{column:t,title:"Thành phố"}),cell:({row:t})=>e.jsx("div",{className:"text-sm",children:t.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:t})=>e.jsx(G,{column:t,title:"Cấu hình buffet"}),cell:({row:t})=>{var a;const s=t.original;return((a=s.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(I,{variant:"outline",size:"sm",onClick:()=>l(s),className:"h-6 px-2 text-xs",children:e.jsx(de,{className:"h-3 w-3"})})]}):e.jsxs(I,{variant:"outline",size:"sm",onClick:()=>l(s),className:"h-7 px-2 text-xs",children:[e.jsx(de,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:t})=>e.jsx(G,{column:t,title:"Customization"}),cell:({row:t,table:s})=>{var h;const i=t.original,a=s.options.meta,r=i.customization_uid,o=(h=a==null?void 0:a.customizations)==null?void 0:h.find(c=>c.id===r);return o?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:o.name}),e.jsx(I,{variant:"outline",size:"sm",onClick:()=>{var c;return(c=a==null?void 0:a.onCustomizationClick)==null?void 0:c.call(a,i)},className:"h-6 px-2 text-xs",children:e.jsx(de,{className:"h-3 w-3"})})]}):e.jsxs(I,{variant:"outline",size:"sm",onClick:()=>{var c;return(c=a==null?void 0:a.onCustomizationClick)==null?void 0:c.call(a,i)},className:"h-7 px-2 text-xs",children:[e.jsx(de,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:t,table:s})=>{const i=t.original,a=s.options.meta;return e.jsxs(I,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:r=>{var o;r.stopPropagation(),(o=a==null?void 0:a.onCopyClick)==null||o.call(a,i)},children:[e.jsx(Ft,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",i.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:t})=>e.jsx(Ve,{column:t,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:t,table:s})=>{const i=t.original,a=t.getValue("isActive"),r=s.options.meta;return e.jsx("div",{onClick:o=>{var h;o.stopPropagation(),(h=r==null?void 0:r.onToggleStatus)==null||h.call(r,i)},className:"cursor-pointer",children:e.jsx(Dt,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:t,table:s})=>{const i=t.original,a=s.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(I,{variant:"ghost",size:"sm",onClick:r=>{var o;r.stopPropagation(),(o=a==null?void 0:a.onDeleteClick)==null||o.call(a,i)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(Et,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",i.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],us=hs;function xs({currentPage:l,onPageChange:t,hasNextPage:s}){const i=()=>{l>1&&t(l-1)},a=()=>{s&&t(l+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(I,{variant:"outline",size:"sm",onClick:i,disabled:l===1,className:"flex items-center gap-2",children:[e.jsx(Rt,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:l}),e.jsxs(I,{variant:"outline",size:"sm",onClick:a,disabled:!s,className:"flex items-center gap-2",children:["Sau",e.jsx(Vt,{className:"h-4 w-4"})]})]})}const ps=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],fs=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function gs({table:l,selectedItemTypeUid:t="all",onItemTypeChange:s,selectedCityUid:i="all",onCityChange:a,selectedDaysOfWeek:r=[],onDaysOfWeekChange:o,selectedStatus:h="all",onStatusChange:c,onDeleteSelected:f}){var p;const[j,S]=y.useState(!1),{data:v=[]}=le(),{data:_=[]}=re(),w=_.filter(n=>n.active===1),x=w.map(n=>({label:n.city_name,value:n.id})),g=w.map(n=>n.id).join(",");y.useEffect(()=>{i==="all"&&g&&a&&a(g)},[i,g,a]);const T=l.getState().columnFilters.length>0,P=l.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[P>0&&e.jsxs(I,{variant:"destructive",size:"sm",onClick:f,className:"h-9",children:[e.jsx(Se,{}),"Xóa món (",P,")"]}),e.jsx(zt,{placeholder:"Tìm kiếm món ăn...",value:((p=l.getColumn("name"))==null?void 0:p.getFilterValue())??"",onChange:n=>{var C;return(C=l.getColumn("name"))==null?void 0:C.setFilterValue(n.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(me,{value:i,onValueChange:n=>{a&&a(n)},children:[e.jsx(he,{className:"h-10 w-[180px]",children:e.jsx(ue,{placeholder:"Chọn thành phố"})}),e.jsxs(xe,{children:[e.jsx(X,{value:g,children:"Tất cả thành phố"}),x.map(n=>e.jsx(X,{value:n.value,children:n.label},n.value))]})]}),e.jsxs(me,{value:h,onValueChange:c,children:[e.jsx(he,{className:"h-10 w-[180px]",children:e.jsx(ue,{placeholder:"Chọn Trạng thái"})}),e.jsx(xe,{children:fs.map(n=>e.jsx(X,{value:n.value,children:n.label},n.value))})]}),e.jsxs(I,{variant:"outline",size:"sm",onClick:()=>S(!j),className:"h-9",children:[e.jsx(Ht,{className:"h-4 w-4"}),"Nâng cao"]}),T&&e.jsxs(I,{variant:"ghost",onClick:()=>l.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(Kt,{className:"ml-2 h-4 w-4"})]})]})}),j&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(me,{value:t,onValueChange:s,children:[e.jsx(he,{className:"h-10 w-[180px]",children:e.jsx(ue,{placeholder:"Chọn loại món"})}),e.jsxs(xe,{children:[e.jsx(X,{value:"all",children:"Tất cả nhóm món"}),v.filter(n=>n.active===1).map(n=>({label:n.item_type_name,value:n.id})).map(n=>e.jsx(X,{value:n.value,children:n.label},n.value))]})]}),e.jsx(Lt,{options:ps,value:r,onValueChange:o||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function _s({columns:l,data:t,onCustomizationClick:s,onCopyClick:i,onToggleStatus:a,onRowClick:r,onDeleteClick:o,customizations:h,selectedItemTypeUid:c,onItemTypeChange:f,selectedCityUid:j,onCityChange:S,selectedDaysOfWeek:v,onDaysOfWeekChange:_,selectedStatus:w,onStatusChange:x,hasNextPageOverride:g,currentPage:T,onPageChange:P}){var K;const[p,n]=y.useState({}),[C,m]=y.useState({}),[E,V]=y.useState([]),[L,b]=y.useState([]),[u,d]=y.useState(!1),{deleteMultipleItemsAsync:D}=it(),A=()=>{d(!0)},M=async()=>{try{const z=B.getFilteredSelectedRowModel().rows.map(U=>U.original.id);await D(z),d(!1),B.resetRowSelection()}catch{}},O=(k,z)=>{const U=z.target;U.closest('input[type="checkbox"]')||U.closest("button")||U.closest('[role="button"]')||U.closest(".badge")||U.tagName==="BUTTON"||r==null||r(k)},B=ze({data:t,columns:l,state:{sorting:L,columnVisibility:C,rowSelection:p,columnFilters:E},enableRowSelection:!0,onRowSelectionChange:n,onSortingChange:b,onColumnFiltersChange:V,onColumnVisibilityChange:m,getCoreRowModel:Le(),getFilteredRowModel:Bt(),getSortedRowModel:Pt(),getFacetedRowModel:Ot(),getFacetedUniqueValues:Mt(),meta:{onCustomizationClick:s,onCopyClick:i,onToggleStatus:a,onDeleteClick:o,customizations:h}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(gs,{table:B,selectedItemTypeUid:c,onItemTypeChange:f,selectedCityUid:j,onCityChange:S,selectedDaysOfWeek:v,onDaysOfWeekChange:_,selectedStatus:w,onStatusChange:x,onDeleteSelected:A}),e.jsxs(ge,{className:"rounded-md border",children:[e.jsxs(_e,{className:"relative",children:[e.jsx(ye,{children:B.getHeaderGroups().map(k=>e.jsx(q,{children:k.headers.map(z=>e.jsx(ae,{colSpan:z.colSpan,children:z.isPlaceholder?null:pe(z.column.columnDef.header,z.getContext())},z.id))},k.id))}),e.jsx(je,{children:(K=B.getRowModel().rows)!=null&&K.length?B.getRowModel().rows.map(k=>e.jsx(q,{"data-state":k.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:z=>O(k.original,z),children:k.getVisibleCells().map(z=>e.jsx(W,{children:pe(z.column.columnDef.cell,z.getContext())},z.id))},k.id)):e.jsx(q,{children:e.jsx(W,{colSpan:l.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx($,{orientation:"horizontal"})]}),e.jsx(xs,{currentPage:T??1,onPageChange:k=>P&&P(k),hasNextPage:!!g}),e.jsx(He,{open:u,onOpenChange:d,title:`Bạn có chắc muốn xóa ${B.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:M,destructive:!0})]})}function ys(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-8 w-[250px]"}),e.jsx(N,{className:"h-8 w-[100px]"}),e.jsx(N,{className:"h-8 w-[100px]"}),e.jsx(N,{className:"h-8 w-[100px]"})]}),e.jsx(N,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(N,{className:"h-4 w-8"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-32"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((l,t)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(N,{className:"h-4 w-8"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-32"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-16"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-20"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-24"}),e.jsx(N,{className:"h-4 w-16"})]})},t))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(N,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(N,{className:"h-8 w-[100px]"}),e.jsx(N,{className:"h-8 w-8"}),e.jsx(N,{className:"h-8 w-8"})]})]})]})}function js({open:l,onOpenChange:t,data:s,onSave:i}){var b;const[a,r]=y.useState(s),[o,h]=y.useState([]),[c,f]=y.useState(!1),{company:j,brands:S}=ne(u=>u.auth),v=S==null?void 0:S[0],{bulkCreateItemsInCity:_,isBulkCreating:w}=nt(),{data:x=[]}=le({skip_limit:!0}),{data:g=[]}=we({skip_limit:!0}),{data:T=[]}=be(),{data:P=[]}=re();Oe.useEffect(()=>{r(s)},[s]),y.useEffect(()=>{if(s&&s.length>0){const u=s[0]||[],D=s.slice(1).map(A=>{const M={};return u.forEach((O,B)=>{M[String(O)]=A[B]||""}),M});h(D)}},[s]);const p=u=>{if(typeof u=="string"){const d=u.toLowerCase().trim();return d==="có"||d==="yes"||d==="1"||d==="true"?1:0}return u?1:0},n=async()=>{if(i){i(a),F.success("Data saved successfully"),t(!1);return}if(!(j!=null&&j.id)||!(v!=null&&v.id)){F.error("Thiếu thông tin công ty hoặc thương hiệu");return}if(o.length===0){F.error("Không có dữ liệu để import");return}f(!0);try{const u=o.map(d=>{const D=P.find(k=>k.city_name===d["Thành phố"]);if(!D)throw new Error(`Không tìm thấy thành phố: ${d["Thành phố"]}`);const A=T.find(k=>k.unit_id===d["Đơn vị"]),M=T.find(k=>k.unit_id==="MON"),O=x.find(k=>k.item_type_id===d.Nhóm||k.item_type_name===d.Nhóm),B=x.find(k=>k.item_type_name==="LOẠI KHÁC"),K=g.find(k=>k.item_class_id===d["Loại món"]||k.item_class_name===d["Loại món"]);return{company_uid:j.id,brand_uid:v.id,city_uid:D.id,item_id:d["Mã món"],unit_uid:(A==null?void 0:A.id)||(M==null?void 0:M.id)||"",ots_price:Number(d.Giá)||0,ta_price:Number(d.Giá)||0,ots_tax:(Number(d["VAT (%)"])||0)/100,ta_tax:(Number(d["VAT (%)"])||0)/100,item_name:d.Tên,item_id_barcode:d["Mã barcode"]||"",is_eat_with:p(d["Món ăn kèm"]),item_type_uid:(O==null?void 0:O.id)||(B==null?void 0:B.id)||"",item_class_uid:(K==null?void 0:K.id)||null,description:d["Mô tả"]||"",item_id_mapping:String(d.SKU||""),time_cooking:(Number(d["Thời gian chế biến (phút)"])||0)*6e4,time_sale_date_week:Number(d.Ngày)||0,time_sale_hour_day:Number(d.Giờ)||0,sort:Number(d["Thứ tự"])||1,image_path_thumb:"",image_path:d["Hình ảnh"]||"",extra_data:{no_update_quantity_toping:p(d["Không cập nhật số lượng món ăn kèm"]),enable_edit_price:p(d["Cho phép sửa giá khi bán"]),is_virtual_item:p(d["Cấu hình món ảo"]),is_item_service:p(d["Cấu hình món dịch vụ"]),is_buffet_item:p(d["Cấu hình món ăn là vé buffet"])}}});await _(u,{onSuccess:()=>{f(!1),t(!1)},onError:d=>{console.error("Error creating items:",d),f(!1)}})}catch(u){console.error("Error creating items:",u),F.error(u instanceof Error?u.message:"Có lỗi xảy ra khi tạo món ăn")}},C=()=>{t(!1)},m=Oe.useCallback(u=>{const d=a.filter((D,A)=>A!==u);r(d)},[a]),{tableData:E,columns:V}=y.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const u=a[0]||[],d=a.slice(1),D=[{id:"actions",header:"-",cell:({row:M})=>e.jsx(I,{variant:"ghost",size:"sm",onClick:()=>m(M.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(kt,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...u.map((M,O)=>({id:`col_${O}`,accessorKey:`col_${O}`,header:String(M),cell:({row:B})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:B.getValue(`col_${O}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:d.map((M,O)=>{const B={_originalIndex:O+1};return M.forEach((K,k)=>{B[`col_${k}`]=K}),B}),columns:D}},[a,m]),L=ze({data:E,columns:V,getCoreRowModel:Le()});return!a||a.length===0?null:e.jsx(J,{open:l,onOpenChange:t,children:e.jsxs(Y,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(Z,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(ee,{className:"text-xl font-semibold",children:"Thêm món từ file"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ge,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(_e,{children:[e.jsx(ye,{className:"sticky top-0 z-10 bg-white",children:L.getHeaderGroups().map(u=>e.jsx(q,{children:u.headers.map(d=>{var D;return e.jsx(ae,{className:((D=d.column.columnDef.meta)==null?void 0:D.className)||"",children:d.isPlaceholder?null:pe(d.column.columnDef.header,d.getContext())},d.id)})},u.id))}),e.jsx(je,{children:(b=L.getRowModel().rows)!=null&&b.length?L.getRowModel().rows.map(u=>e.jsx(q,{className:"hover:bg-muted/50",children:u.getVisibleCells().map(d=>{var D;return e.jsx(W,{className:((D=d.column.columnDef.meta)==null?void 0:D.className)||"",children:pe(d.column.columnDef.cell,d.getContext())},d.id)})},u.id)):e.jsx(q,{children:e.jsx(W,{colSpan:V.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx($,{orientation:"horizontal"}),e.jsx($,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(I,{variant:"outline",onClick:C,children:"Đóng"}),e.jsx(I,{onClick:n,disabled:c||w,className:"bg-green-600 hover:bg-green-700",children:c||w?"Đang lưu...":"Lưu"})]})]})]})})}function ws(){const{open:l,setOpen:t}=fe(),[s,i]=y.useState(!1),[a,r]=y.useState([]),o=y.useRef(null),{data:h=[]}=le(),{data:c=[]}=we(),{data:f=[]}=be(),{data:j=[]}=re(),{downloadImportTemplateAsync:S,isPending:v}=lt(),_=async()=>{try{await S({itemTypes:h,itemClasses:c,units:f,cities:j})}catch{F.error("Lỗi khi tải template")}},w=()=>{var g;(g=o.current)==null||g.click()},x=g=>{var p;const T=(p=g.target.files)==null?void 0:p[0];if(!T)return;const P=new FileReader;P.onload=n=>{var C;try{const m=new Uint8Array((C=n.target)==null?void 0:C.result),E=Ie(m,{type:"array"}),V=E.SheetNames[0],L=E.Sheets[V],b=Te.sheet_to_json(L,{defval:"",raw:!1});if(b.length===0){F.error("File không có dữ liệu");return}const u=new Set,d=b.map(D=>{const A=(D["Mã món"]??"").toString().trim();A&&u.add(A);let M=A;if(!M){let O=Pe();for(;u.has(O);)O=Pe();u.add(O),M=O}return{...D,"Mã món":M}});if(d.length>0){const D=Object.keys(d[0]),A=[D,...d.map(M=>D.map(O=>M[O]||""))];r(A)}else r([]);t(null),i(!0),o.current&&(o.current.value="")}catch{F.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},P.readAsArrayBuffer(T)};return e.jsxs(e.Fragment,{children:[e.jsx(J,{open:l==="import",onOpenChange:g=>t(g?"import":null),children:e.jsxs(Y,{className:"max-w-2xl",children:[e.jsx(Z,{children:e.jsx(ee,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(I,{variant:"outline",size:"sm",onClick:_,disabled:v,className:"flex items-center gap-2",children:v?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(Ce,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(I,{variant:"outline",size:"sm",onClick:w,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(ke,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:o,type:"file",accept:".xlsx,.xls",onChange:x,style:{display:"none"}}),e.jsx(js,{open:s,onOpenChange:i,data:a})]})}function bs({open:l,onOpenChange:t,data:s}){const[i,a]=y.useState(s),[r,o]=y.useState(!1),{user:h,company:c}=ne(p=>p.auth),{selectedBrand:f}=Xe(),{mutate:j,isPending:S}=rt(),{data:v=[]}=le({skip_limit:!0}),{data:_=[]}=we({skip_limit:!0}),{data:w=[]}=be(),{data:x=[]}=re();y.useEffect(()=>{a(s)},[s]);const g=p=>{a(n=>n.filter((C,m)=>m!==p))},T=async()=>{if(!c||!f){F.error("Thiếu thông tin cần thiết để cập nhật");return}o(!0);const p=i.map(n=>{const C=w.find(u=>u.unit_id===n.unit_id),m=x.find(u=>u.city_name===n.city_name),E=v.find(u=>u.item_type_id===n.item_type_id||u.item_type_name===n.item_type_name),V=_.find(u=>u.item_class_id===n.item_class_id||u.item_class_name===n.item_class_name),L=w.find(u=>u.unit_id==="MON"),b=v.find(u=>u.item_type_name==="LOẠI KHÁC");return{item_id:n.item_id,item_name:n.item_name,description:n.description||"",ots_price:n.ots_price||0,ots_tax:(n.ots_tax||0)/100,ta_price:n.ots_price||0,ta_tax:(n.ots_tax||0)/100,time_sale_hour_day:String(n.time_sale_hour_day??0),time_sale_date_week:String(n.time_sale_date_week??0),allow_take_away:1,is_eat_with:n.is_eat_with||0,image_path:n.image_path||"",image_path_thumb:n.image_path?`${n.image_path}?width=185`:"",item_color:"",list_order:n.list_order||0,is_service:n.is_item_service||0,is_material:0,active:n.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:n.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:n.is_print_label||0,quantity_limit:0,is_kit:0,time_cooking:(n.time_cooking||0)*6e4,item_id_barcode:n.item_id_barcode||"",process_index:0,is_allow_discount:n.is_allow_discount||0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(n.sku||""),effective_date:0,expire_date:0,sort:n.list_order||1,sort_online:1e3,extra_data:{cross_price:n.cross_price||[],formula_qrcode:n.inqr_formula||"",is_buffet_item:n.is_buffet_item||0,up_size_buffet:[],is_item_service:n.is_item_service||0,is_virtual_item:n.is_virtual_item||0,price_by_source:n.price_by_source||[],enable_edit_price:n.price_change||0,exclude_items_buffet:n.exclude_items_buffet||[],no_update_quantity_toping:n.no_update_quantity_toping||0},revision:0,unit_uid:(C==null?void 0:C.id)||(L==null?void 0:L.id)||"",unit_secondary_uid:null,item_type_uid:(E==null?void 0:E.id)||(b==null?void 0:b.id)||"",item_class_uid:(V==null?void 0:V.id)||void 0,source_uid:null,brand_uid:f.id,city_uid:(m==null?void 0:m.id)||"",company_uid:c.id,customization_uid:n.customization_uid||"",is_fabi:1,deleted:!1,created_by:(h==null?void 0:h.email)||"",updated_by:(h==null?void 0:h.email)||"",deleted_by:null,created_at:n.created_at||Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,cities:m?[{id:m.id,city_id:m.city_id||"",fb_city_id:m.fb_city_id||"",city_name:m.city_name,image_path:m.image_path,description:m.description||"",active:m.active||1,extra_data:m.extra_data,revision:m.revision||0,sort:m.sort||0,created_by:m.created_by,updated_by:m.updated_by,deleted_by:m.deleted_by,created_at:m.created_at||0,updated_at:m.updated_at||0,deleted_at:m.deleted_at,items_cities:{item_uid:n.id,city_uid:m.id}}]:[],id:n.id}});j(p,{onSuccess:()=>{o(!1),t(!1)},onError:n=>{console.error("Error updating items:",n),F.error(`Có lỗi xảy ra khi cập nhật món ăn: ${n}`),o(!1)}})},P=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(J,{open:l,onOpenChange:t,children:e.jsxs(Y,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(Z,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(ee,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(ge,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(_e,{children:[e.jsx(ye,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(q,{children:[e.jsx(ae,{className:"w-12"}),P.map(p=>e.jsx(ae,{style:{width:p.width},children:p.label},p.key))]})}),e.jsx(je,{children:i.map((p,n)=>e.jsxs(q,{children:[e.jsx(W,{children:e.jsx(I,{variant:"ghost",size:"icon",onClick:()=>g(n),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Se,{className:"h-4 w-4"})})}),P.map(C=>e.jsx(W,{style:{width:C.width},children:(()=>{var E;const m=p[C.key];return C.key==="ots_price"?e.jsxs("span",{className:"text-right",children:[((E=Number(m))==null?void 0:E.toLocaleString("vi-VN"))||0," ₫"]}):C.key==="active"?e.jsx("span",{children:m}):C.key==="item_id"||C.key==="item_id_barcode"?e.jsx("span",{className:"font-mono text-sm",children:m||""}):C.key==="item_name"?e.jsx("span",{className:"font-medium",children:m||""}):["is_eat_with","no_update_quantity_toping","price_change","is_virtual_item","is_item_service","is_buffet_item"].includes(C.key)?e.jsx("span",{className:"text-center",children:m}):e.jsx("span",{children:m||""})})()},C.key))]},n))})]}),e.jsx($,{orientation:"horizontal"}),e.jsx($,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(I,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(I,{onClick:T,disabled:r||S,children:r||S?"Đang lưu...":"Lưu"})]})]})]})})}function Ns({open:l,onOpenChange:t}){const[s,i]=y.useState("all"),[a,r]=y.useState("all"),[o,h]=y.useState("all"),[c,f]=y.useState([]),[j,S]=y.useState(!1),v=y.useRef(null),{data:_=[]}=le(),{data:w=[]}=we(),{data:x=[]}=be(),{data:g=[]}=re(),{fetchItemsDataAsync:T,isPending:P}=ct(),p=[{label:"Tất cả nhóm món",value:"all"},..._.filter(b=>b.active===1).map(b=>({label:b.item_type_name,value:b.id}))],n=[{label:"Tất cả thành phố",value:"all"},...g.filter(b=>b.active===1).map(b=>({label:b.city_name,value:b.id}))],C=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],m=async()=>{try{const b=await T({city_uid:s!=="all"?s:void 0,item_type_uid:a!=="all"?a:void 0,active:o!=="all"?o:void 0});await ot({itemTypes:_,itemClasses:w,units:x},b),F.success("Tải file thành công!")}catch{F.error("Lỗi khi tải file")}},E=b=>({ID:"id","Mã món":"item_id","Thành phố":"city_name",Tên:"item_name",Giá:"ots_price","Trạng thái":"active","Mã barcode":"item_id_barcode","Món ăn kèm":"is_eat_with","Không cập nhật số lượng món ăn kèm":"no_update_quantity_toping","Đơn vị":"unit_name",Nhóm:"item_type_id","Tên nhóm":"item_type_name","Loại món":"item_class_id","Tên loại":"item_class_name","Mô tả":"description",SKU:"sku","VAT (%)":"ots_tax","Thời gian chế biến (phút)":"time_cooking","Cho phép sửa giá khi bán":"price_change","Cấu hình món ảo":"is_virtual_item","Cấu hình món dịch vụ":"is_item_service","Cấu hình món ăn là vé buffet":"is_buffet_item",Giờ:"time_sale_hour_day",Ngày:"time_sale_date_week","Thứ tự":"list_order","Hình ảnh":"image_path","Công thức inQR cho máy pha trà":"inqr_formula"})[b]||b.toLowerCase().replace(/\s+/g,"_"),V=b=>{var D;const u=(D=b.target.files)==null?void 0:D[0];if(!u)return;const d=new FileReader;d.onload=A=>{var M;try{const O=new Uint8Array((M=A.target)==null?void 0:M.result),B=Ie(O,{type:"array"}),K=B.SheetNames[0],k=B.Sheets[K],z=Te.sheet_to_json(k,{header:1});if(z.length>0){const U=z,R=U[0]||[],ce=U.slice(1).map((Ke,Ne)=>{const se={id:`temp_${Ne}`};return R.forEach((Ee,Ue)=>{const H=E(String(Ee)),oe=Ke[Ue];Ne===0&&console.log(`Header: "${Ee}" -> Key: "${H}", Value: "${oe}"`),H==="ots_price"||H==="ots_tax"||H==="time_cooking"||H==="time_sale_hour_day"||H==="time_sale_date_week"||H==="list_order"||H==="active"||H==="is_eat_with"||H==="no_update_quantity_toping"||H==="price_change"||H==="is_virtual_item"||H==="is_item_service"||H==="is_buffet_item"?se[H]=Number(oe)||0:se[H]=oe||""}),Ne===0&&console.log("First item after processing:",se),se});f(ce),S(!0),F.success("File uploaded successfully")}}catch{F.error("Error parsing file")}},d.readAsArrayBuffer(u)},L=()=>{var b;(b=v.current)==null||b.click()};return e.jsxs(e.Fragment,{children:[e.jsx(J,{open:l,onOpenChange:t,children:e.jsxs(Y,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(Z,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(ee,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(ve,{options:n,value:s,onValueChange:i,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(ve,{options:p,value:a,onValueChange:r,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(ve,{options:C,value:o,onValueChange:h,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsxs(I,{variant:"outline",size:"sm",onClick:m,disabled:P,className:"flex items-center gap-2",children:[e.jsx(Ce,{className:"h-4 w-4"}),P&&"Đang tải..."]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(I,{variant:"outline",size:"sm",onClick:L,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(ke,{className:"h-4 w-4"})]}),e.jsx("input",{ref:v,type:"file",accept:".xlsx,.xls",onChange:V,style:{display:"none"}})]})]})]})]})}),e.jsx(bs,{open:j,onOpenChange:S,data:c})]})}function vs({open:l,onOpenChange:t,data:s,originalItems:i,sources:a}){const[r,o]=y.useState(s),{bulkUpdatePriceBySource:h,isUpdating:c}=ds();y.useEffect(()=>{o(s)},[s]);const f=_=>{o(w=>w.filter((x,g)=>g!==_))},j=async()=>{if(r.length===0){F.error("Không có dữ liệu để lưu");return}try{await h({previewItems:r,originalItems:i,sources:a}),t(!1)}catch(_){console.error("Error saving price by source configuration:",_)}},v=(()=>{if(r.length===0)return[];const _=r[0],w=[];return w.push({key:"item_uid",label:"Item UID",width:"w-32"},{key:"item_id",label:"Item ID",width:"w-24"},{key:"item_name",label:"Tên món",width:"w-48"},{key:"item_type_name",label:"Nhóm món",width:"w-32"},{key:"ots_price",label:"Giá gốc",width:"w-24"},{key:"ots_tax",label:"Vat (%)",width:"w-20"}),["ZALO [10000045]","FACEBOOK [10000049]","SO [10000134]","CRM [10000162]","VNPAY [10000165]","GOJEK (GOVIET) [10000168]","ShopeeFood [10000169]","MANG VỀ [10000171]","TẠI CHỖ [10000172]","CALL CENTER [10000176]","O2O [10000216]","BEFOOD [10000253]"].forEach(g=>{_.hasOwnProperty(g)&&w.push({key:g,label:g,width:"w-32"})}),w.push({key:"actions",label:"Thao tác",width:"w-20"}),w})();return e.jsx(J,{open:l,onOpenChange:t,children:e.jsxs(Y,{className:"max-w-7xl max-h-[90vh] flex flex-col",children:[e.jsx(Z,{className:"flex-shrink-0",children:e.jsxs(ee,{className:"text-xl font-semibold",children:["Xem trước cấu hình giá theo nguồn (",r.length," món)"]})}),e.jsxs("div",{className:"flex-1 flex flex-col min-h-0",children:[e.jsx("div",{className:"flex-1 border rounded-lg",children:e.jsxs(ge,{className:"h-full",children:[e.jsxs(_e,{children:[e.jsx(ye,{className:"sticky top-0 bg-white z-10",children:e.jsx(q,{children:v.map(_=>e.jsx(ae,{className:`${_.width||"w-auto"} text-center font-medium`,children:_.label},_.key))})}),e.jsx(je,{children:r.map((_,w)=>e.jsx(q,{className:"hover:bg-gray-50",children:v.map(x=>{var g;return e.jsx(W,{className:"text-center text-sm",children:x.key==="actions"?e.jsx(I,{variant:"ghost",size:"sm",onClick:()=>f(w),className:"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",children:e.jsx(Se,{className:"h-4 w-4"})}):x.key==="ots_price"?e.jsx("span",{children:((g=_[x.key])==null?void 0:g.toLocaleString("vi-VN"))||""}):x.key.includes("[")?e.jsx("span",{children:_[x.key]&&typeof _[x.key]=="number"?_[x.key].toLocaleString("vi-VN"):_[x.key]||""}):e.jsxs("span",{children:[!x.key.includes("[")&&x.key!=="item_uid"&&x.key!=="item_id"&&x.key!=="item_name"&&x.key!=="item_type_name"&&x.key!=="ots_price"&&x.key!=="ots_tax"&&e.jsx("span",{children:_[x.key]||""}),(x.key==="item_uid"||x.key==="item_id"||x.key==="item_name"||x.key==="item_type_name"||x.key==="ots_tax")&&e.jsx("span",{children:_[x.key]||""})]})},x.key)})},`${_.item_uid}-${w}`))})]}),e.jsx($,{orientation:"horizontal"}),e.jsx($,{orientation:"vertical"})]})}),e.jsxs("div",{className:"flex flex-shrink-0 items-center justify-between border-t pt-4",children:[e.jsx(I,{variant:"outline",onClick:()=>t(!1),children:"Đóng"}),e.jsx(I,{onClick:j,disabled:c||r.length===0,children:c?"Đang lưu...":"Lưu"})]})]})]})})}function Cs({open:l,onOpenChange:t,cities:s}){var C;const[i,a]=y.useState(""),[r,o]=y.useState(null),[h,c]=y.useState(!1),[f,j]=y.useState(!1),[S,v]=y.useState([]),[_,w]=y.useState(!1),{items:x,sources:g,itemTypes:T,isLoading:P}=cs(i,!!i),p=async()=>{if(!i){F.error("Vui lòng chọn thành phố trước");return}if(P){F.error("Đang tải dữ liệu, vui lòng chờ...");return}if(!x.length){F.error("Không có món nào trong thành phố này");return}try{c(!0);const m=s.find(V=>V.id===i),E=(m==null?void 0:m.city_name)||"Unknown";await es(x,T,E),F.success("Đã tải xuống file template thành công")}catch(m){console.error("Error downloading template:",m),F.error("Có lỗi xảy ra khi tải xuống file template")}finally{c(!1)}},n=()=>{if(!i){F.error("Vui lòng chọn thành phố trước");return}const m=document.createElement("input");m.type="file",m.accept=".xlsx,.xls",m.onchange=async E=>{var b;const V=(b=E.target.files)==null?void 0:b[0];if(!V)return;const L=as(V);if(!L.isValid){F.error(L.error);return}try{j(!0),o(V);const u=await ss(V);if(u.length===0){F.error("File không có dữ liệu hợp lệ");return}v(u),w(!0),t(!1),F.success(`Đã xử lý file thành công: ${u.length} món`)}catch(u){console.error("Error processing file:",u),F.error("Có lỗi xảy ra khi xử lý file")}finally{j(!1)}},m.click()};return e.jsxs(J,{open:l,onOpenChange:t,children:[e.jsxs(Y,{className:"max-w-2xl",children:[e.jsx(Z,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(ee,{className:"text-xl font-semibold",children:"Thêm cấu hình giá món theo nguồn đơn"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 1. Chọn thành phố"}),e.jsxs(me,{value:i,onValueChange:a,children:[e.jsx(he,{className:"w-full",children:e.jsx(ue,{placeholder:"Chọn thành phố"})}),e.jsx(xe,{children:s.map(m=>e.jsx(X,{value:m.id,children:m.city_name},m.id))})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 2. Tải file dữ liệu để lấy món và nguồn đã có"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"})}),e.jsxs(I,{variant:"outline",onClick:p,disabled:h||!i||P,className:"flex items-center gap-2",children:[e.jsx(Wt,{className:"h-4 w-4"}),h?"Đang tải...":P?"Đang tải dữ liệu...":"Tải xuống"]})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Nhập giá theo nguồn tương ứng với từng món vào cột ",e.jsx("strong",{children:"price"}),"."]}),e.jsx("p",{children:"Bỏ trống hoặc xoá dòng với những nguồn không có cấu hình giá."}),e.jsx("p",{children:e.jsx("strong",{className:"text-red-600",children:"Không sửa các cột item_uid, item_name."})})]})]}),e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-3 text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),r&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",r.name]})]}),e.jsxs(I,{onClick:n,disabled:!i||f,className:"flex items-center gap-2",children:[e.jsx(Qt,{className:"h-4 w-4"}),f?"Đang xử lý...":"Tải file lên"]})]})]})]})]}),e.jsx(vs,{open:_,onOpenChange:w,data:S,originalItems:x,sources:g,cityName:(C=s.find(m=>m.id===i))==null?void 0:C.city_name})]})}function ks(){const{open:l,setOpen:t,currentRow:s,setCurrentRow:i}=fe(),{deleteItemAsync:a}=dt(),{data:r=[]}=Ut();return e.jsxs(e.Fragment,{children:[e.jsx(Ns,{open:l==="export-dialog",onOpenChange:()=>t(null)}),e.jsx(ws,{}),e.jsx(Cs,{open:l==="price-by-source-config",onOpenChange:()=>t(null),cities:r}),s&&e.jsx(e.Fragment,{children:e.jsx(He,{destructive:!0,open:l==="delete",onOpenChange:o=>{o||(t(null),setTimeout(()=>{i(null)},500))},handleConfirm:async()=>{t(null),setTimeout(()=>{i(null)},500),await a(s.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function Ss(){const l=Ae(),[t,s]=y.useState(1),{setOpen:i,setCurrentRow:a}=fe(),{updateStatusAsync:r}=ht(),{updateItemAsync:o}=ut(),{isCustomizationDialogOpen:h,isBuffetItem:c,isBuffetConfigModalOpen:f,setIsCustomizationDialogOpen:j,setIsBuffetItem:S,selectedMenuItem:v,setSelectedMenuItem:_,setIsBuffetConfigModalOpen:w,selectedBuffetMenuItem:x,setSelectedBuffetMenuItem:g,selectedItemTypeUid:T,setSelectedItemTypeUid:P,selectedCityUid:p,setSelectedCityUid:n,selectedDaysOfWeek:C,setSelectedDaysOfWeek:m,selectedStatus:E,setSelectedStatus:V}=is(),L=y.useMemo(()=>({...T!=="all"&&{item_type_uid:T},...p!=="all"&&{city_uid:p},...C.length>0&&{time_sale_date_week:C.join(",")},...E!=="all"&&{active:parseInt(E,10)},page:t}),[T,p,C,E,t]);y.useEffect(()=>{s(1)},[T,p,C,E]);const{data:b=[],isLoading:u,error:d,hasNextPage:D}=xt({params:L}),{data:A=[]}=Ye({skip_limit:!0,list_city_uid:p!=="all"?[p]:void 0}),M=R=>{_(R),j(!0)},O=R=>{var te,ce;_(R),g(((te=R==null?void 0:R.extra_data)==null?void 0:te.exclude_items_buffet)||[]),S(((ce=R==null?void 0:R.extra_data)==null?void 0:ce.is_buffet_item)===1),w(!0)},B=R=>{l({to:"/menu/items/items-in-city/detail",search:{id:R.id||""}})},K=R=>{a(R),i("delete")},k=R=>{l({to:"/menu/items/items-in-city/detail/$id",params:{id:R.id||""}})},z=async R=>{const te=R.active?0:1;await r({id:R.id||"",active:te})},U=us({onBuffetConfigClick:O});return d?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:d&&`Món ăn: ${(d==null?void 0:d.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(Ze,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(st,{}),e.jsx(at,{}),e.jsx(tt,{})]})}),e.jsxs(et,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(ms,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[u&&e.jsx(ys,{}),!u&&e.jsx(_s,{columns:U,data:b,onCustomizationClick:M,onCopyClick:B,onToggleStatus:z,onRowClick:k,onDeleteClick:K,customizations:A,selectedItemTypeUid:T,onItemTypeChange:P,selectedCityUid:p,onCityChange:n,selectedDaysOfWeek:C,onDaysOfWeekChange:m,selectedStatus:E,onStatusChange:V,hasNextPageOverride:D,currentPage:t,onPageChange:s})]})]}),e.jsx(ks,{}),h&&v&&e.jsx(pt,{open:h,onOpenChange:j,item:v,customizations:A}),f&&x&&e.jsx(ft,{itemsBuffet:x,open:f,onOpenChange:w,onItemsChange:async R=>{await o({...v,extra_data:{is_buffet_item:c?1:0,exclude_items_buffet:R}})},items:b,hide:!1,enable:c,onEnableChange:S})]})}function Is(){return e.jsx(mt,{children:e.jsx(Ss,{})})}const Qa=Is;export{Qa as component};
