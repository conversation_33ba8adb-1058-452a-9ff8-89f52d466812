import{aR as m,j as i}from"./index-sntk-7aJ.js";import"./pos-api-CQfNAror.js";import"./vietqr-api-C-lVxzD-.js";import"./user-B67nntxu.js";import"./crm-api-DhFa_vPG.js";import"./header-CePLmjHC.js";import"./main-BTno3738.js";import"./search-context-iBulh32Z.js";import"./date-range-picker-CBqQlAZr.js";import"./form-2rWd-Izg.js";import{C as p}from"./create-table-form-DHfwgvTt.js";import"./separator-BIRyiZJ0.js";import"./command-Ct8kkkRi.js";import"./calendar-BhUTNdpd.js";import"./createLucideIcon-CvoWT756.js";import"./index-CyrU-3zB.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-HHE8oSxh.js";import"./search-DFsa4jPY.js";import"./createReactComponent-BcntBX1O.js";import"./scroll-area-CPF3eFT1.js";import"./index-S3x6QFUG.js";import"./select-ZzLBlgJd.js";import"./index-2BmiXKhT.js";import"./check-Dykbakem.js";import"./IconChevronRight-CuIdFTCw.js";import"./chevron-right-JsGDE6eB.js";import"./react-icons.esm-BTYMKzFL.js";import"./popover-DUo0D-5L.js";import"./use-areas-BpqI-r-O.js";import"./useQuery-CPo_FvE_.js";import"./utils-km2FGkQ4.js";import"./useMutation-jqWRauQa.js";import"./images-api-CY2_Ph94.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-Bd0bhmIo.js";import"./use-tables-xE7VJtFB.js";import"./input-DCw8aMl6.js";import"./checkbox-gkM78Twn.js";import"./collapsible-DwXQUepC.js";import"./use-items-in-store-data-DdEUc9t0.js";import"./use-item-types-DlwgINfg.js";import"./use-item-classes-DVU3n-Lf.js";import"./use-units-DSbsiywC.js";import"./use-removed-items-DUNjx3Y6.js";import"./items-in-store-api-DAi66Dmg.js";import"./xlsx-DkH2s96g.js";import"./copy-Lzq5DVyx.js";import"./plus-DwYoxQhJ.js";import"./minus-CQNqZ182.js";const mo=function(){const{store_uid:o,area_uid:t,tableLayout:r}=m.useSearch();return i.jsx(p,{storeUid:o,areaId:t,fromTableLayout:r})};export{mo as component};
