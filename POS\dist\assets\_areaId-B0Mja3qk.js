import{aY as t,j as i}from"./index-sntk-7aJ.js";import"./pos-api-CQfNAror.js";import"./header-CePLmjHC.js";import"./main-BTno3738.js";import"./search-context-iBulh32Z.js";import"./date-range-picker-CBqQlAZr.js";import"./form-2rWd-Izg.js";import{A as m}from"./area-form-Cp8Sts8J.js";import"./separator-BIRyiZJ0.js";import"./command-Ct8kkkRi.js";import"./calendar-BhUTNdpd.js";import"./createLucideIcon-CvoWT756.js";import"./index-CyrU-3zB.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-HHE8oSxh.js";import"./search-DFsa4jPY.js";import"./createReactComponent-BcntBX1O.js";import"./scroll-area-CPF3eFT1.js";import"./index-S3x6QFUG.js";import"./select-ZzLBlgJd.js";import"./index-2BmiXKhT.js";import"./check-Dykbakem.js";import"./IconChevronRight-CuIdFTCw.js";import"./chevron-right-JsGDE6eB.js";import"./react-icons.esm-BTYMKzFL.js";import"./popover-DUo0D-5L.js";import"./images-api-CY2_Ph94.js";import"./use-areas-BpqI-r-O.js";import"./useQuery-CPo_FvE_.js";import"./utils-km2FGkQ4.js";import"./useMutation-jqWRauQa.js";import"./query-keys-3lmd-xp6.js";import"./input-DCw8aMl6.js";const B=function(){const{areaId:o}=t.useParams(),{store_uid:r}=t.useSearch();return console.log("🔍 AreaDetailPage rendered"),console.log("📍 Route params:",{areaId:o}),console.log("🔍 Route search:",{store_uid:r}),console.log("📊 Full URL:",window.location.href),i.jsx(m,{areaId:o,storeUid:r})};export{B as component};
