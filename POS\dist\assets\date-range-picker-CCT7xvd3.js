import{r as d,j as s,B as g}from"./index-sntk-7aJ.js";import{I as P}from"./input-DCw8aMl6.js";import{P as R,a as z,b as B}from"./popover-DUo0D-5L.js";import{C as H}from"./calendar-Bccd0kH4.js";import{a as L,C as q}from"./chevron-right-JsGDE6eB.js";import{f as h,g as W,r as j,v as A,l as F,q as x}from"./isSameMonth-C8JQo-AN.js";import{s as G,e as J,i as M}from"./subMonths-BRhS7Uii.js";function _({startDate:c,endDate:a,onDateChange:y,dateRange:N,onDateRangeChange:C}){const[w,p]=d.useState(!1),[t,f]=d.useState(null),[n,m]=d.useState(null),[i,v]=d.useState(new Date),S=e=>{p(e),e||(f(null),m(null))},D=e=>{!t||t&&a?(f(e),m(null),y(e,null)):(e>=t?y(t,e):y(e,t),f(null),m(null),p(!1))},T=()=>{const e=A(i),r=F(i),o=new Date(e);o.setDate(o.getDate()-e.getDay());const u=new Date(r);return u.setDate(u.getDate()+(6-r.getDay())),J({start:o,end:u})},O=e=>{if(c&&a)return M(e,{start:c,end:a});if(t&&n){const r=t<n?t:n,o=t<n?n:t;return M(e,{start:r,end:o})}return!1},I=e=>c&&x(e,c)?"start":a&&x(e,a)?"end":t&&x(e,t)?"temp":!1,k=e=>{const r=I(e),o=O(e),u=j(e,i),E=x(e,new Date),b=n&&x(e,n);let l="h-8 w-8 p-0 text-xs font-normal transition-colors ";return u?l+="text-gray-900 ":l+="text-gray-400 ",r==="start"||r==="end"?l+="bg-blue-500 text-white hover:bg-blue-600 ":r==="temp"?l+="bg-blue-400 text-white hover:bg-blue-500 ":b&&t?l+="bg-blue-400 text-white ":o?l+="bg-blue-100 text-blue-900 hover:bg-blue-200 ":l+="hover:bg-gray-100 ",E&&!r&&!o&&!b&&(l+="text-red-500 font-bold "),l};return s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(P,{value:N,onChange:e=>C(e.target.value),placeholder:"dd/mm/yyyy - dd/mm/yyyy",className:"w-64 text-xs"}),s.jsxs(R,{open:w,onOpenChange:S,children:[s.jsx(z,{asChild:!0,children:s.jsx(H,{className:"h-4 w-4 text-gray-500 cursor-pointer hover:text-gray-700"})}),s.jsx(B,{className:"w-auto p-4",align:"end",children:s.jsxs("div",{className:"space-y-4",children:[c&&a&&s.jsxs("div",{className:"text-center text-sm font-medium text-blue-600 bg-blue-50 p-2 rounded",children:[h(c,"dd/MM/yyyy")," - ",h(a,"dd/MM/yyyy")]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(g,{variant:"outline",size:"sm",onClick:()=>v(G(i)),className:"h-7 w-7 p-0",children:s.jsx(L,{className:"h-4 w-4"})}),s.jsx("div",{className:"text-sm font-medium",children:h(i,"MMMM yyyy")}),s.jsx(g,{variant:"outline",size:"sm",onClick:()=>v(W(i,1)),className:"h-7 w-7 p-0",children:s.jsx(q,{className:"h-4 w-4"})})]}),s.jsxs("div",{className:"grid grid-cols-7 gap-1",children:[["T2","T3","T4","T5","T6","T7","CN"].map(e=>s.jsx("div",{className:"text-center text-xs font-medium text-gray-500 p-2",children:e},e)),T().map((e,r)=>s.jsx("div",{className:"text-center",children:s.jsx(g,{variant:"ghost",className:k(e),onClick:()=>D(e),onMouseEnter:()=>{t&&!a&&j(e,i)&&m(e)},onMouseLeave:()=>{t&&!a&&m(null)},children:e.getDate()})},r))]}),s.jsx("div",{className:"text-xs text-gray-500 text-center border-t pt-3",children:t&&n?s.jsxs("span",{className:"text-blue-600 font-medium",children:[h(t<n?t:n,"dd/MM/yyyy")," - ",h(t<n?n:t,"dd/MM/yyyy")]}):t?"Chọn ngày kết thúc":"Chọn ngày bắt đầu"})]})})]})]})}export{_ as D};
