import{j as e,B as f,h as pe,r as C,l as oe,b as de,a as ge,c as Q,T as fe,o as G,p as W,q as X}from"./index-sntk-7aJ.js";import{v as L}from"./date-range-picker-CBqQlAZr.js";import{L as N}from"./form-2rWd-Izg.js";import{u as je,b as me,c as U,d as Ce}from"./create-combo-dialog-CDDltrDs.js";import{X as ve,C as J}from"./calendar-BhUTNdpd.js";import{j as Ne,g as ye,k as be,l as _e,m as Te}from"./use-combos-BS0ko-KO.js";import{I as Y}from"./input-DCw8aMl6.js";import{S as Z,a as B,b as ee,c as se,d as ae,C as S}from"./select-ZzLBlgJd.js";import{T as Se,a as we,c as te}from"./tabs-B5_0MbuP.js";import{b as H}from"./pos-api-CQfNAror.js";import"./vietqr-api-C-lVxzD-.js";import{u as De}from"./use-sales-channels-Bd0bhmIo.js";import"./user-B67nntxu.js";import"./crm-api-DhFa_vPG.js";import{u as V}from"./useQuery-CPo_FvE_.js";import{Q as q}from"./query-keys-3lmd-xp6.js";import{P as ke}from"./modal-Yuq_-Dyf.js";import{C as b}from"./checkbox-gkM78Twn.js";import{C as w,a as D,b as k}from"./collapsible-DwXQUepC.js";import{C as E}from"./chevron-right-JsGDE6eB.js";import{P as ne,a as ie,b as le}from"./popover-DUo0D-5L.js";import{C as ce}from"./calendar-Bccd0kH4.js";import{f as M}from"./isSameMonth-C8JQo-AN.js";import{C as re}from"./circle-help-DDGU4UNg.js";import{j as z}from"./date-utils-DBbLjCz0.js";function Ee(){const{handleBack:a,handleSave:o}=je(),{isEditMode:c,isLoading:h,isFormValid:l}=me();return e.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[e.jsx(f,{variant:"ghost",size:"sm",onClick:a,className:"flex items-center",children:e.jsx(ve,{className:"h-4 w-4"})}),e.jsxs("h1",{className:"text-3xl font-bold",children:[c&&"Chỉnh sửa chương trình giảm giá",!c&&"Tạo chương trình giảm giá"]}),e.jsxs(f,{type:"button",disabled:h||!l,className:"min-w-[100px]",onClick:o,children:[h&&c&&"Đang cập nhật...",h&&!c&&"Đang tạo...",!h&&c&&"Cập nhật",!h&&!c&&"Lưu"]})]})}function Fe({comboId:a,initialStoreUid:o}={}){const c=pe(),h=!!a,{data:l,isLoading:d}=Ne(),[r,t]=C.useState({storeUid:o||"",channelUid:"",comboType:"PERCENT",comboPercentage:0,comboAmount:0,startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],marketingDays:[],marketingHours:[],promotionUid:"",promotionName:"",filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""}}),i=g=>new Date(g+"T00:00:00+07:00");C.useEffect(()=>{},[l,h]);const{validateFormData:m}=_e(),{mutate:x,isPending:n}=ye(),{mutate:u,isPending:s}=be(a||"",{onSuccess:()=>{c({to:"/sale/combo"})},onError:()=>{}}),p=()=>{c({to:"/sale/combo"})},y=()=>{const g=r.marketingDays.length>0?z.convertDayStringsToNumbers(r.marketingDays):[0,1,2,3,4,5,6],v=r.marketingHours.length>0?z.convertHourStringsToNumbers(r.marketingHours):z.getAllHours();return{comboType:r.comboType,comboValue:r.comboType==="PERCENT"?r.comboPercentage/100:r.comboAmount,fromDate:i(r.startDate),toDate:i(r.endDate),selectedDays:g,selectedHours:v,saleChannelUid:r.channelUid,promotionUid:r.promotionUid||"1576be99-992c-4085-a68c-105f7fbf0fff",filterState:r.filterState}},F=g=>({package_detail:{LstItem_Options:[]},deleted:!1,extra_data:{price_by_source:[]},vat_tax_rate:0,item_type_uid:"",from_date:g.fromDate.getTime(),to_date:g.toDate.getTime(),promotion_id:"",time_sale_date_week:g.selectedDays.reduce((v,_)=>v|1<<_,0),time_sale_hour_day:g.selectedHours.reduce((v,_)=>v|1<<_,0),sort:1e3,package_name:`Combo ${Date.now()}`,ots_value:g.comboValue,description:"",company_uid:"",brand_uid:"",package_id:`COMBO-${Date.now()}`,ta_value:g.comboValue,store_uid:r.storeUid,promotion_uid:g.promotionUid,use_same_data:0}),K=async()=>{if(P){const g=y(),v=F(g);if(m(g).length>0)return;x(v,{onSuccess:()=>{c({to:"/sale/combo"})},onError:()=>{}})}},$=g=>{t(v=>({...v,...g}))},P=r.storeUid!==""&&r.channelUid!==""&&(r.comboType==="PERCENT"?r.comboPercentage>0:r.comboAmount>0);return{formData:r,updateFormData:$,handleBack:p,handleSave:K,isFormValid:P,isLoading:n||s||d,isEditMode:h}}function Pe({storeUid:a}){const{selectedBrand:o}=oe(),{company:c}=de(),{data:h=[],isLoading:l}=De({companyUid:c==null?void 0:c.id,brandUid:o==null?void 0:o.id,storeUid:a,partnerConfig:1,skipLimit:!0});return{salesChannels:h,isLoadingChannels:l}}function Ae(){var i;const{formData:a,updateFormData:o}=U(),{isEditMode:c}=me(),{currentBrandStores:h}=ge(),{salesChannels:l,isLoadingChannels:d}=Pe({storeUid:a.storeUid}),{createPromotion:r}=Te(),t=async m=>{o({channelUid:m});const x=l.find(n=>n.id===m);x&&a.storeUid&&await r({storeUid:a.storeUid,channelUid:m,channelName:x.sourceName})};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Thông tin giảm giá"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(N,{className:"min-w-[200px] text-sm font-medium",children:["Cửa hàng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(Z,{value:a.storeUid,onValueChange:m=>{o({storeUid:m,channelUid:""})},disabled:c,children:[e.jsx(B,{className:"flex-1",children:e.jsx(ee,{placeholder:"Chọn cửa hàng"})}),e.jsx(se,{children:h.map(m=>e.jsx(ae,{value:m.id,children:m.store_name},m.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(N,{className:"min-w-[200px] text-sm font-medium",children:["Kênh ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(Z,{value:a.channelUid,onValueChange:t,disabled:!a.storeUid||d||c,children:[e.jsx(B,{className:"flex-1",children:e.jsx(ee,{placeholder:a.storeUid?d?"Đang tải...":"Chọn kênh":"Chọn cửa hàng trước"})}),e.jsx(se,{children:l.map(m=>e.jsx(ae,{value:m.id,children:m.sourceName},m.id))})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(N,{className:"min-w-[200px] text-sm font-medium",children:["Chương trình khuyến mãi ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(Y,{value:c&&a.promotionName?a.promotionName:a.channelUid&&l.length>0?`CTKM tự động theo kênh ${((i=l.find(m=>m.id===a.channelUid))==null?void 0:i.sourceName)||""}`:"CTKM được tự động tạo theo kênh",disabled:!0,className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(N,{className:"min-w-[200px] text-sm font-medium",children:[a.comboType==="PERCENT"?"Phần trăm giảm giá":"Số tiền giảm giá"," ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"flex flex-1 gap-2",children:[e.jsx(Y,{type:"number",min:"0",max:a.comboType==="PERCENT"?"100":void 0,value:a.comboType==="PERCENT"?a.comboPercentage||"":a.comboAmount||"",onChange:m=>{const x=Number(m.target.value);if(a.comboType==="PERCENT"){const n=x>100?100:x;o({comboPercentage:n})}else o({comboAmount:x})},placeholder:"0",className:"flex-1"}),e.jsx(Se,{value:a.comboType,onValueChange:m=>o({comboType:m}),className:"w-auto",children:e.jsxs(we,{className:"grid w-fit grid-cols-2",children:[e.jsx(te,{value:"PERCENT",children:"%"}),e.jsx(te,{value:"AMOUNT",children:"đ"})]})})]})]})]})}function Le({itemTypes:a,selectedItems:o,searchTerm:c,onItemToggle:h}){const[l,d]=C.useState(!1),[r,t]=C.useState(!1),i=(Array.isArray(a)?a:[]).filter(n=>{const u=n.item_type_name||n.name||"";return u.toLowerCase()!=="uncategory"&&u.toLowerCase().includes(c.toLowerCase())}),m=i.filter(n=>o.includes(n.item_type_id||n.id)),x=i.filter(n=>!o.includes(n.item_type_id||n.id));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(w,{open:!l,onOpenChange:n=>d(!n),children:[e.jsx(D,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",m.length,")"]}),l&&e.jsx(E,{className:"h-4 w-4"}),!l&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(k,{className:"max-h-40 space-y-2 overflow-y-auto",children:[m.map(n=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${n.active===0?"opacity-50":""}`,children:[e.jsx(b,{checked:!0,onCheckedChange:()=>h(n.item_type_id||n.id),disabled:n.active===0}),e.jsx("span",{className:"text-sm",children:n.item_type_name||n.name||"Không có tên"})]},n.id)),m.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn nhóm nào"})]})]}),e.jsxs(w,{open:!r,onOpenChange:n=>t(!n),children:[e.jsx(D,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",x.length,")"]}),r&&e.jsx(E,{className:"h-4 w-4"}),!r&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(k,{className:"max-h-40 space-y-2 overflow-y-auto",children:[x.map(n=>e.jsxs("div",{className:`flex items-center space-x-2 p-2 ${n.active===0?"opacity-50":""}`,children:[e.jsx(b,{checked:!1,onCheckedChange:()=>h(n.item_type_id||n.id),disabled:n.active===0}),e.jsx("span",{className:"text-sm",children:n.item_type_name||n.name||"Không có tên"})]},n.id)),x.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có nhóm nào"})]})]})]})}function Me({items:a,selectedItems:o,searchTerm:c,isLoading:h,onItemToggle:l}){const[d,r]=C.useState(!1),[t,i]=C.useState(!1),x=(Array.isArray(a)?a:[]).filter(s=>{const p=(s==null?void 0:s.item_name)||(s==null?void 0:s.name)||"";return s.active!==0&&p.toLowerCase().includes(c.toLowerCase())}).map(s=>({...s,name:s.item_name||s.name||"Không có tên"})),n=x.filter(s=>o.includes(s.item_id||s.id)),u=x.filter(s=>!o.includes(s.item_id||s.id));return h?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(w,{open:!d,onOpenChange:s=>r(!s),children:[e.jsx(D,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",n.length,")"]}),d&&e.jsx(E,{className:"h-4 w-4"}),!d&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(k,{className:"max-h-40 space-y-2 overflow-y-auto",children:[n.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!0,onCheckedChange:()=>l(s.item_id||s.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),n.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món ăn nào"})]})]}),e.jsxs(w,{open:!t,onOpenChange:s=>i(!s),children:[e.jsx(D,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",u.length,")"]}),t&&e.jsx(E,{className:"h-4 w-4"}),!t&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(k,{className:"max-h-40 space-y-2 overflow-y-auto",children:[u.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!1,onCheckedChange:()=>l(s.item_id||s.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),u.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món ăn nào"})]})]})]})}function Ue({packages:a,selectedItems:o,searchTerm:c,isLoading:h,onItemToggle:l}){const[d,r]=C.useState(!1),[t,i]=C.useState(!1),x=(Array.isArray(a)?a:[]).filter(s=>{const y=((s==null?void 0:s.package_name)||(s==null?void 0:s.name)||"").toLowerCase().includes(c.toLowerCase());return s.active!==0&&y}).map(s=>({...s,name:s.package_name||s.name||"Không có tên"})),n=x.filter(s=>o.includes(s.package_id||s.id)),u=x.filter(s=>!o.includes(s.package_id||s.id));return h?e.jsx("div",{className:"py-4 text-center text-sm text-gray-500",children:"Đang tải..."}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs(w,{open:!d,onOpenChange:s=>r(!s),children:[e.jsx(D,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",n.length,")"]}),d&&e.jsx(E,{className:"h-4 w-4"}),!d&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(k,{className:"max-h-40 space-y-2 overflow-y-auto",children:[n.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!0,onCheckedChange:()=>l(s.package_id||s.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),n.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn combo nào"})]})]}),e.jsxs(w,{open:!t,onOpenChange:s=>i(!s),children:[e.jsx(D,{asChild:!0,children:e.jsxs(f,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",u.length,")"]}),t&&e.jsx(E,{className:"h-4 w-4"}),!t&&e.jsx(S,{className:"h-4 w-4"})]})}),e.jsxs(k,{className:"max-h-40 space-y-2 overflow-y-auto",children:[u.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(b,{checked:!1,onCheckedChange:()=>l(s.package_id||s.id)}),e.jsx("span",{className:"text-sm",children:e.jsx("span",{className:"font-medium",children:s.name||"Không có tên"})})]},s.id)),u.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có combo nào"})]})]})]})}function Ke({open:a,onOpenChange:o,storeUid:c,onSave:h,initialApplyToAll:l=!1,initialActiveFilter:d=null,initialSelectedItems:r=[]}){const{selectedBrand:t}=oe(),{company:i}=de(),[m,x]=C.useState(""),[n,u]=C.useState(!1),[s,p]=C.useState(null),[y,F]=C.useState([]),{data:K=[],isLoading:$}=V({queryKey:[q.ITEM_TYPES,i==null?void 0:i.id,t==null?void 0:t.id,c],queryFn:async()=>!(i!=null&&i.id)||!(t!=null&&t.id)||!c?[]:(await H.get(`/mdata/v1/item-types?skip_limit=true&company_uid=${i.id}&brand_uid=${t.id}&store_uid=${c}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(t!=null&&t.id)&&!!c}),{data:P=[],isLoading:g}=V({queryKey:[q.ITEMS,i==null?void 0:i.id,t==null?void 0:t.id,c],queryFn:async()=>!(i!=null&&i.id)||!(t!=null&&t.id)||!c?[]:(await H.get(`/mdata/v1/items?skip_limit=true&company_uid=${i.id}&brand_uid=${t.id}&list_store_uid=${c}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(t!=null&&t.id)&&!!c}),{data:v=[],isLoading:_}=V({queryKey:[q.PACKAGES,i==null?void 0:i.id,t==null?void 0:t.id,c],queryFn:async()=>!(i!=null&&i.id)||!(t!=null&&t.id)||!c?[]:(await H.get(`/mdata/v1/packages?skip_limit=true&company_uid=${i.id}&brand_uid=${t.id}&store_uid=${c}`)).data.data||[],enabled:a&&!!(i!=null&&i.id)&&!!(t!=null&&t.id)&&!!c}),I=$||g||_;C.useEffect(()=>{a&&(x(""),u(l),p(d),F(r))},[a,l,d,r]);const he=j=>{u(j),j&&p(null)},O=j=>{u(!1),p(T=>{const A=T===j?null:j;return A!==T&&F([]),A})},R=j=>{F(T=>T.includes(j)?T.filter(A=>A!==j):[...T,j])},ue=()=>{o(!1)},xe=()=>{n?h(["all"],!0,null):h(y,!1,s),o(!1)};return e.jsx(ke,{title:"Áp dụng cho",open:a,onOpenChange:o,onCancel:ue,onConfirm:xe,cancelText:"Hủy",confirmText:"Lưu",maxWidth:"sm:max-w-2xl",isLoading:I,confirmDisabled:!n&&y.length===0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(b,{id:"apply-all",checked:n,onCheckedChange:he}),e.jsx(N,{htmlFor:"apply-all",className:"text-sm font-medium",children:"Áp dụng cho tất cả món và nhóm"})]}),e.jsx(Y,{placeholder:"Tìm kiếm",value:m,onChange:j=>x(j.target.value),className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(N,{className:"text-sm font-medium",children:"Áp dụng cho"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(f,{type:"button",variant:s==="groups"?"default":"outline",size:"sm",onClick:()=>O("groups"),children:"Nhóm"}),e.jsx(f,{type:"button",variant:s==="items"?"default":"outline",size:"sm",onClick:()=>O("items"),children:"Món ăn"}),e.jsx(f,{type:"button",variant:s==="packages"?"default":"outline",size:"sm",onClick:()=>O("packages"),children:"Combo"})]})]}),!n&&e.jsxs("div",{className:"space-y-2",children:[s==="groups"&&e.jsx(Le,{itemTypes:K,selectedItems:y,searchTerm:m,onItemToggle:R}),s==="items"&&e.jsx(Me,{items:P,selectedItems:y,searchTerm:m,isLoading:I,onItemToggle:R}),s==="packages"&&e.jsx(Ue,{packages:v,selectedItems:y,searchTerm:m,isLoading:I,onItemToggle:R})]})]})})}function $e(){var x;const{formData:a,updateFormData:o}=U(),[c,h]=C.useState(!1),l=()=>{h(!0)},d=(n,u,s)=>{const p={...a.filterState,is_all:u?1:0,is_type:0,is_item:0,is_combo:0,type_id:"",item_id:"",combo_id:""};!u&&n.length>0&&s&&(s==="groups"?(p.is_type=1,p.type_id=n.join(",")):s==="items"?(p.is_item=1,p.item_id=n.join(",")):s==="packages"&&(p.is_combo=1,p.combo_id=n.join(","))),o({filterState:p})},r=((x=a.filterState)==null?void 0:x.is_all)===1,t=()=>{var n,u,s;return((n=a.filterState)==null?void 0:n.is_type)===1?"groups":((u=a.filterState)==null?void 0:u.is_item)===1?"items":((s=a.filterState)==null?void 0:s.is_combo)===1?"packages":null},i=()=>{var n,u,s;return((n=a.filterState)==null?void 0:n.is_type)===1&&a.filterState.type_id?a.filterState.type_id.split(",").filter(p=>p.trim()):((u=a.filterState)==null?void 0:u.is_item)===1&&a.filterState.item_id?a.filterState.item_id.split(",").filter(p=>p.trim()):((s=a.filterState)==null?void 0:s.is_combo)===1&&a.filterState.combo_id?a.filterState.combo_id.split(",").filter(p=>p.trim()):[]},m=()=>{if(r)return"Áp dụng cho tất cả";const n=i();if(n.length>0){const u=t(),s=u==="groups"?"nhóm":u==="items"?"món":"combo";return`${n.length} ${s}`}return"Thêm"};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Tuỳ chỉnh"}),e.jsx("div",{className:"mb-4 text-sm text-gray-600",children:"Áp dụng giảm giá tự động cho các món hoặc nhóm món, combo cụ thể"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(N,{className:"min-w-[200px] text-sm font-medium",children:["Áp dụng cho ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{type:"button",variant:"outline",onClick:l,disabled:!a.storeUid,className:"flex-1 justify-start",children:m()})]}),e.jsx(Ke,{open:c,onOpenChange:h,storeUid:a.storeUid,onSave:d,initialApplyToAll:r,initialActiveFilter:t(),initialSelectedItems:i()})]})}function Ie(){const{formData:a,updateFormData:o}=U(),c=new Date;c.setHours(0,0,0,0);const h=a.startDate?new Date(a.startDate):void 0,l=a.endDate?new Date(a.endDate):void 0,d=t=>{if(t){const i=M(t,"yyyy-MM-dd");o({startDate:i})}},r=t=>{if(t){const i=M(t,"yyyy-MM-dd");o({endDate:i})}};return e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Ngày áp dụng"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(N,{className:"min-w-[120px] text-sm font-medium",children:"Ngày bắt đầu"}),e.jsxs(ne,{children:[e.jsx(ie,{asChild:!0,children:e.jsxs(f,{variant:"outline",className:Q("flex-1 justify-start text-left font-normal",!h&&"text-muted-foreground"),children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),h?M(h,"dd/MM/yyyy",{locale:L}):"Chọn ngày bắt đầu"]})}),e.jsx(le,{className:"w-auto p-0",align:"start",children:e.jsx(J,{mode:"single",selected:h,onSelect:d,disabled:t=>t>c,initialFocus:!0,locale:L})})]})]}),e.jsxs("div",{className:"flex flex-1 items-center gap-4",children:[e.jsx(N,{className:"min-w-[120px] text-sm font-medium",children:"Ngày kết thúc"}),e.jsxs(ne,{children:[e.jsx(ie,{asChild:!0,children:e.jsxs(f,{variant:"outline",className:Q("flex-1 justify-start text-left font-normal",!l&&"text-muted-foreground"),children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),l?M(l,"dd/MM/yyyy",{locale:L}):"Chọn ngày kết thúc"]})}),e.jsx(le,{className:"w-auto p-0",align:"start",children:e.jsx(J,{mode:"single",selected:l,onSelect:r,disabled:t=>t<c,initialFocus:!0,locale:L})})]})]})]})]})}const Oe=[{label:"T2",value:"0"},{label:"T3",value:"1"},{label:"T4",value:"2"},{label:"T5",value:"3"},{label:"T6",value:"4"},{label:"T7",value:"5"},{label:"CN",value:"6"}],Re=[{value:"0"},{value:"1"},{value:"2"},{value:"3"},{value:"4"},{value:"5"},{value:"6"},{value:"7"},{value:"8"},{value:"9"},{value:"10"},{value:"11"},{value:"12"},{value:"13"},{value:"14"},{value:"15"},{value:"16"},{value:"17"},{value:"18"},{value:"19"},{value:"20"},{value:"21"},{value:"22"},{value:"23"}];function He(){const{formData:a,updateFormData:o}=U(),c=l=>{const d=a.marketingDays||[],r=d.includes(l)?d.filter(t=>t!==l):[...d,l];o({marketingDays:r})},h=l=>{const d=a.marketingHours||[],r=d.includes(l)?d.filter(t=>t!==l):[...d,l];o({marketingHours:r})};return e.jsx(fe,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Khung thời gian áp dụng"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"text-sm font-medium text-gray-500",children:"Chọn ngày"}),e.jsxs(G,{children:[e.jsx(W,{asChild:!0,children:e.jsx(re,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(X,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các ngày trong tuần"})})]})]}),e.jsx("div",{className:"flex gap-2",children:Oe.map(l=>{var d;return e.jsx(f,{type:"button",variant:(d=a.marketingDays)!=null&&d.includes(l.value)?"default":"outline",size:"sm",onClick:()=>c(l.value),className:"flex-1",children:l.label},l.value)})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"text-sm font-medium text-gray-500",children:"Chọn giờ"}),e.jsxs(G,{children:[e.jsx(W,{asChild:!0,children:e.jsx(re,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(X,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Nếu để trống mặc định sẽ áp dụng tất cả các giờ trong ngày"})})]})]}),e.jsx("div",{className:"grid grid-cols-6 gap-2",children:Re.map(l=>{var d;return e.jsxs(f,{type:"button",variant:(d=a.marketingHours)!=null&&d.includes(l.value)?"default":"outline",size:"sm",onClick:()=>h(l.value),className:"text-xs",children:[l.value,":00"]},l.value)})})]})]})})}function xs({comboId:a,storeUid:o}={}){const c=Fe({comboId:a,initialStoreUid:o});return e.jsx(Ce,{value:c,children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ee,{}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ae,{}),e.jsx($e,{}),e.jsx(Ie,{}),e.jsx(He,{})]})})})]})})}export{xs as C};
