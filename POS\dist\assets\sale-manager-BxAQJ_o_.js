import{j as e}from"./index-sntk-7aJ.js";const n=function(){return e.jsxs("div",{className:"container mx-auto p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Bảng kê hóa đơn"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Tổng hợp và quản lý danh sách hóa đơn bán hàng"})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Bảng kê hóa đơn"}),e.jsx("p",{className:"text-gray-500",children:"Nội dung báo cáo sẽ được phát triển ở giai đoạn tiếp theo"})]})})]})};export{n as component};
