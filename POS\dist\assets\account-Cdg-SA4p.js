import{j as t,B as m,r as c,L as p}from"./index-sntk-7aJ.js";import{H as l}from"./header-CePLmjHC.js";import{P as d}from"./profile-dropdown-CteBDQaM.js";import{S as h,T as x}from"./search-BAjrNsV6.js";import"./date-range-picker-CBqQlAZr.js";import"./form-2rWd-Izg.js";import{D as g}from"./data-table-cWrShXSR.js";import{B as u}from"./badge-BulEkXWC.js";import{S as j}from"./settings-D6TN9hKu.js";import{u as f,n as v,A as y}from"./navigation-JII3TAX8.js";import"./separator-BIRyiZJ0.js";import"./avatar-CcvCHous.js";import"./dropdown-menu-Cq_TXEG8.js";import"./index-S3x6QFUG.js";import"./index-CyrU-3zB.js";import"./index-DBwfoC6_.js";import"./check-Dykbakem.js";import"./createLucideIcon-CvoWT756.js";import"./search-context-iBulh32Z.js";import"./command-Ct8kkkRi.js";import"./calendar-BhUTNdpd.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-HHE8oSxh.js";import"./search-DFsa4jPY.js";import"./createReactComponent-BcntBX1O.js";import"./pos-api-CQfNAror.js";import"./scroll-area-CPF3eFT1.js";import"./select-ZzLBlgJd.js";import"./index-2BmiXKhT.js";import"./IconChevronRight-CuIdFTCw.js";import"./IconSearch-BFRP7UUl.js";import"./chevron-right-JsGDE6eB.js";import"./react-icons.esm-BTYMKzFL.js";import"./popover-DUo0D-5L.js";import"./table-pagination-B5B41d4R.js";import"./pagination-CMmkpgKj.js";import"./table-BEvnnqp-.js";function T({users:i,isLoading:s,onEditUser:o,onToggleStatus:a}){const n=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:r=>t.jsx(u,{variant:r==="active"?"default":"secondary",children:r==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(r,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>o(e),className:"h-8 w-8",children:t.jsx(j,{className:"h-4 w-4"})}),t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>a(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(g,{data:i,columns:n,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}const N=({error:i})=>t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:i})})}),k=()=>t.jsx("div",{className:"mb-6",children:t.jsx(p,{to:y.CREATE,children:t.jsx(m,{children:"Tạo tài khoản"})})});function S(){const{users:i,isLoading:s,error:o,toggleUserStatus:a}=f(),n=c.useCallback(e=>{v(e.id)},[]),r=c.useCallback(async e=>{await a(e)},[a]);return o?t.jsx(N,{error:o}):t.jsxs(t.Fragment,{children:[t.jsxs(l,{children:[t.jsx(h,{}),t.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[t.jsx(x,{}),t.jsx(d,{})]})]}),t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(k,{}),t.jsx(T,{users:i,isLoading:s,onEditUser:n,onToggleStatus:r})]})]})}const mt=S;export{mt as component};
