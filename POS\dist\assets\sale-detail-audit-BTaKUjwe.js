import{j as e,c as C,B as S,a as A,r as h,b as re,l as ce}from"./index-sntk-7aJ.js";import{T as me,a as de,c as K,b as D}from"./tabs-B5_0MbuP.js";import{H as he}from"./header-CePLmjHC.js";import{M as ue}from"./main-BTno3738.js";import{e as xe,g as W,a as ge,u as fe,d as q,R as je,c as ve,T as pe}from"./use-all-stores-DdvQVqYu.js";import{P as Ne}from"./profile-dropdown-CteBDQaM.js";import{S as be,T as ye}from"./search-BAjrNsV6.js";import{L as H}from"./form-2rWd-Izg.js";import{S as Se,a as _e,b as we,c as Te,d as B}from"./select-ZzLBlgJd.js";import{C as Y}from"./calendar-BhUTNdpd.js";import{P as Q,a as J,b as X}from"./popover-DUo0D-5L.js";import{C as Z}from"./calendar-Bccd0kH4.js";import{f as k,v as O,l as Ce}from"./isSameMonth-C8JQo-AN.js";import{v as T}from"./date-range-picker-CBqQlAZr.js";import{u as He}from"./useQuery-CPo_FvE_.js";import{s as Me}from"./sales-api-D01ijVCg.js";import{f as Ve,a as Ke}from"./stores-api-j41S6mEL.js";import{B as ee}from"./badge-BulEkXWC.js";import{D as F}from"./data-table-column-header-DeuI7A-H.js";import{u as De,g as ke,a as Fe,c as Pe,b as Ae,d as Ie,e as Le,f as se}from"./index-C8dGDLCq.js";import{T as te,a as ze,b as P,c as Ee,d as ae,e as l}from"./table-BEvnnqp-.js";import{D as Ge}from"./data-table-pagination-LbJ7fTUC.js";import{S as o}from"./skeleton-BrdgPuty.js";import"./index-DBwfoC6_.js";import"./index-S3x6QFUG.js";import"./separator-BIRyiZJ0.js";import"./dropdown-menu-Cq_TXEG8.js";import"./index-CyrU-3zB.js";import"./check-Dykbakem.js";import"./createLucideIcon-CvoWT756.js";import"./createReactComponent-BcntBX1O.js";import"./avatar-CcvCHous.js";import"./search-context-iBulh32Z.js";import"./command-Ct8kkkRi.js";import"./dialog-HHE8oSxh.js";import"./search-DFsa4jPY.js";import"./pos-api-CQfNAror.js";import"./scroll-area-CPF3eFT1.js";import"./IconChevronRight-CuIdFTCw.js";import"./IconSearch-BFRP7UUl.js";import"./index-2BmiXKhT.js";import"./chevron-right-JsGDE6eB.js";import"./react-icons.esm-BTYMKzFL.js";import"./utils-km2FGkQ4.js";function Re({dateRange:a,onDateRangeChange:s,filterType:t="daily",className:n}){const m=i=>{s(i(a))},r=(i,u)=>{if(i)if(t==="monthly")if(u==="from"){const c=O(i);m(d=>({from:c,to:d.to}))}else{const c=Ce(i);m(d=>({from:d.from,to:c}))}else m(c=>({...c,[u]:i}))};return e.jsxs("div",{className:C("space-y-4",n),children:[e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{className:"text-sm font-medium",children:t==="monthly"?"Từ tháng":"Từ ngày"}),e.jsxs(Q,{children:[e.jsx(J,{asChild:!0,children:e.jsxs(S,{variant:"outline",className:C("w-fit justify-start text-left font-normal",!a.from&&"text-muted-foreground"),children:[e.jsx(Z,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:a.from?t==="monthly"?k(a.from,"MM/yyyy",{locale:T}):k(a.from,"dd/MM/yyyy",{locale:T}):t==="monthly"?"Chọn tháng bắt đầu":"Chọn ngày bắt đầu"})]})}),e.jsx(X,{className:"w-auto p-0",align:"start",children:e.jsx(Y,{mode:"single",selected:a.from,onSelect:i=>r(i,"from"),initialFocus:!0,locale:T})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{className:"text-sm font-medium",children:t==="monthly"?"Đến tháng":"Đến ngày"}),e.jsxs(Q,{children:[e.jsx(J,{asChild:!0,children:e.jsxs(S,{variant:"outline",className:C("w-fit justify-start text-left font-normal",!a.to&&"text-muted-foreground"),children:[e.jsx(Z,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:a.to?t==="monthly"?k(a.to,"MM/yyyy",{locale:T}):k(a.to,"dd/MM/yyyy",{locale:T}):t==="monthly"?"Chọn tháng kết thúc":"Chọn ngày kết thúc"})]})}),e.jsx(X,{className:"w-auto p-0",align:"start",children:e.jsx(Y,{mode:"single",selected:a.to,onSelect:i=>r(i,"to"),initialFocus:!0,locale:T,disabled:i=>t==="monthly"?O(i)<O(a.from):i<a.from})})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{className:"text-sm font-medium",children:"Chọn nhanh"}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:flex sm:flex-wrap",children:t==="daily"?e.jsxs(e.Fragment,{children:[e.jsx(S,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(xe()),children:"Hôm nay"}),e.jsx(S,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(W(7)),children:"7 ngày"}),e.jsx(S,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(W(28)),children:"28 ngày"}),e.jsx(S,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const i=new Date,u=new Date(i.getFullYear(),i.getMonth(),1);s({from:u,to:i})},children:"Tháng này"})]}):e.jsxs(e.Fragment,{children:[e.jsx(S,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(ge()),children:"3 tháng gần đây"}),e.jsx(S,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const i=new Date,u=i.getFullYear(),c=new Date(u,i.getMonth()-6,1);s({from:c,to:i})},children:"6 tháng gần đây"})]})})]})]})}function $e({dateRange:a,onDateRangeChange:s,selectedStores:t,onStoreChange:n,filterType:m,onFilterTypeChange:r,className:i}){const{currentBrandStores:u}=A(),{stores:c}=fe(),d=c.length>0?c:u,p=h.useMemo(()=>d.filter(x=>x.active===1),[d]);return e.jsx("div",{className:i,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{className:"text-sm font-medium",children:"Loại bộ lọc"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(S,{variant:m==="monthly"?"default":"outline",size:"sm",onClick:()=>r("monthly"),children:"Theo tháng"}),e.jsx(S,{variant:m==="daily"?"default":"outline",size:"sm",onClick:()=>r("daily"),children:"Theo ngày"})]})]}),e.jsx(Re,{dateRange:a,onDateRangeChange:s,filterType:m}),e.jsx("div",{className:"flex flex-wrap gap-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{htmlFor:"store-select",className:"text-sm font-medium",children:"Cửa hàng"}),e.jsxs(Se,{value:t[0]||"all-stores",onValueChange:x=>n([x]),children:[e.jsx(_e,{id:"store-select",className:"w-[180px]",children:e.jsx(we,{placeholder:"Chọn cửa hàng"})}),e.jsxs(Te,{children:[e.jsx(B,{value:"all-stores",children:"Tất cả cửa hàng"}),p.map(x=>e.jsx(B,{value:x.id,children:x.store_name},x.id)),p.length===0&&e.jsx(B,{value:"no-stores",disabled:!0,children:"Không có cửa hàng"})]})]})]})})]})})}const U={all:["accounting-sales-all"],lists:()=>[...U.all,"list"],list:a=>[...U.lists(),a]};function Be({dateRange:a,selectedStores:s=["all-stores"],autoFetch:t=!0,maxPages:n=50}){const{selectedBrand:m,currentBrandApiStores:r,setApiStores:i}=A(),{company:u}=re(),c=u==null?void 0:u.id,d=m==null?void 0:m.id,p=h.useRef(d),x=h.useRef(c);h.useEffect(()=>{p.current=d,x.current=c},[d,c]),h.useEffect(()=>{(async()=>{if(d&&c&&r.length===0)try{const v=await Ve(c,d);i(v)}catch{}})()},[d,c,r.length,i]);const N=a!=null&&a.from?a.from.getTime():void 0,b=a!=null&&a.to?a.to.getTime():void 0,_=h.useMemo(()=>s.includes("all-stores")?(r==null?void 0:r.map(y=>y.id))||[]:s,[s,r]),M=h.useMemo(()=>{const y=new Map;return r==null||r.forEach(v=>{y.set(v.id,v.store_name)}),y},[r]),f=_.join(","),{data:j,isLoading:ne,error:I,refetch:L}=He({queryKey:U.list({companyId:c,brandId:d,storeUidsString:f,startTime:N,endTime:b,maxPages:n}),queryFn:async()=>{if(!d||!c||!N||!b||!f)return[];const y=[];let v=1;const V=50;for(;v<=n;)try{const E=(await Me.getSalesReport({companyUid:c,brandUid:d,listStoreUid:f,startDate:N,endDate:b,page:v})).data||[];if(E.length===0)break;const le=E.map(w=>{const ie=M.get(w.store_uid),G=r==null?void 0:r.find($=>$.id===w.store_uid),R=Ke().find($=>$.id===w.store_uid),oe=ie||(G==null?void 0:G.store_name)||(R==null?void 0:R.store_name)||w.store_name||`Store ${w.store_uid}`;return{...w,storeName:oe}});if(y.push(...le),E.length<V)break;v++}catch{break}return y},enabled:!!(t&&d&&c&&N&&b&&f),staleTime:0,gcTime:10*60*1e3,refetchOnMount:!0,refetchOnWindowFocus:!1});return h.useEffect(()=>{d&&c&&N&&b&&f&&L()},[d,c,L,N,b,f]),{...h.useMemo(()=>{const y=j||[];let v=0,V=0;return y.forEach(z=>{v+=z.total_amount||0,V+=1}),{data:y,totalAmount:v,totalTransactions:V}},[j]),isLoading:ne,error:(I==null?void 0:I.message)||null,refetch:L}}function g(a){return new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND",minimumFractionDigits:0,maximumFractionDigits:0}).format(a)}function Oe(){return[{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px] text-center",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"tran_id",header:({column:s})=>e.jsx(F,{column:s,title:"Mã hoá đơn"}),cell:({row:s})=>{const t=s.getValue("tran_id"),n=t?`#${t.slice(-5)}`:"";return e.jsx("div",{className:"cursor-help font-mono text-sm",title:t,children:n})},enablePinning:!0,enableHiding:!1},{accessorKey:"tran_no",header:({column:s})=>e.jsx(F,{column:s,title:"Số HĐ"}),cell:({row:s})=>e.jsx("div",{className:"font-mono text-sm",children:s.getValue("tran_no")||"-"})},{accessorKey:"shift_id",header:({column:s})=>e.jsx(F,{column:s,title:"Mã ca"}),cell:({row:s})=>{const t=s.getValue("shift_id"),n=t?`#${t.slice(-5)}`:"";return e.jsx("div",{className:"cursor-help font-mono text-sm",title:t,children:n||"-"})}},{accessorKey:"extra_data.peo_count",header:"Số khách",cell:({row:s})=>{var n;const t=((n=s.original.extra_data)==null?void 0:n.peo_count)||0;return e.jsx("div",{className:"text-center",children:t})},enableSorting:!1,enableHiding:!1},{accessorKey:"sale_note",header:"Ghi chú",cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm",title:s.getValue("sale_note"),children:s.getValue("sale_note")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"source_voucher",header:"Nguồn",cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("source_voucher")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"area_name",header:"Khu vực",cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("area_name")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"payment_method_name",header:"PTTT",cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("payment_method_name")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"tran_date",header:"Ngày chứng từ",cell:({row:s})=>{const t=s.getValue("tran_date"),n=t?new Date(t).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"}):"-";return e.jsx("div",{className:"text-sm",children:n})},enableSorting:!1,enableHiding:!1},{accessorKey:"start_hour",header:"Giờ vào",cell:({row:s})=>{const t=s.getValue("start_hour"),n=s.original.start_minute||0,m=t!=null?`${String(t).padStart(2,"0")}:${String(n).padStart(2,"0")}`:"-";return e.jsx("div",{className:"font-mono text-sm",children:m})},enableSorting:!1,enableHiding:!1},{accessorKey:"end_hour",header:"Giờ ra",cell:({row:s})=>{const t=s.getValue("end_hour"),n=s.original.end_minute||0,m=t!=null?`${String(t).padStart(2,"0")}:${String(n).padStart(2,"0")}`:"-";return e.jsx("div",{className:"font-mono text-sm",children:m})},enableSorting:!1,enableHiding:!1},{accessorKey:"amount_origin",header:"Thành tiền",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("amount_origin"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"amount_discount_detail",header:"Giảm giá",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("amount_discount_detail"))}),enableSorting:!1,enableHiding:!1},{id:"discount_percentage",header:"Phần trăm chiết khấu",cell:({row:s})=>{const t=s.original.amount_origin||0,n=s.original.amount_discount_detail||0,m=t>0?n/t*100:0;return e.jsxs("div",{className:"text-right font-medium",children:[m.toFixed(2),"%"]})},enableSorting:!1,enableHiding:!1},{accessorKey:"discount_extra_amount",header:"Chiết khấu",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("discount_extra_amount"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"partner_marketing_amount",header:"Phí hỗ trợ marketing",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("partner_marketing_amount"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"voucher_amount",header:"Phiếu GG",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("voucher_amount"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"service_charge_amount",header:"Phí dịch vụ",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("service_charge_amount"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"vat_amount",header:"Thuế",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("vat_amount"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"discount_vat_amount",header:"Giảm giá VAT",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("discount_vat_amount"))}),enableSorting:!1,enableHiding:!1},{accessorKey:"ship_fee_amount",header:"Phí ship",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("ship_fee_amount"))}),enableSorting:!1,enableHiding:!1},{id:"total_amount_no_vat",header:"Tổng tiền (không bao gồm VAT)",cell:({row:s})=>{const t=s.original.total_amount||0,n=s.original.vat_amount||0,m=t-n;return e.jsx("div",{className:"text-right font-medium",children:g(m)})},enableSorting:!1,enableHiding:!1},{accessorKey:"commission_amount",header:"Hoa hồng",cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("commission_amount"))}),enableSorting:!1,enableHiding:!1},{id:"total_amount_with_commission",header:"Tổng tiền (bao gồm hoa hồng)",cell:({row:s})=>{const t=s.original.total_amount||0,n=s.original.commission_amount||0,m=t+n;return e.jsx("div",{className:"text-right font-medium",children:g(m)})},enableSorting:!1,enableHiding:!1},{accessorKey:"employee_name",header:"Nhân viên",cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("employee_name")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"extra_data.customer_name",header:"Tên khách",cell:({row:s})=>{var n;const t=((n=s.original.extra_data)==null?void 0:n.customer_name)||"";return e.jsx("div",{className:"text-sm",children:t||"-"})},enableSorting:!1,enableHiding:!1},{accessorKey:"voucher_name",header:"Tên CTKM",cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm",title:s.getValue("voucher_name"),children:s.getValue("voucher_name")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"voucher_code",header:"Mã voucher",cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("voucher_code")||"-"}),enableSorting:!1,enableHiding:!1},{accessorKey:"extra_data.customer_phone",header:"Số điện thoại",cell:({row:s})=>{var n;const t=((n=s.original.extra_data)==null?void 0:n.customer_phone)||"";return e.jsx("div",{className:"font-mono text-sm",children:t||"-"})},enableSorting:!1,enableHiding:!1},{accessorKey:"total_amount",header:({column:s})=>e.jsx(F,{column:s,title:"Tổng tiền"}),cell:({row:s})=>e.jsx("div",{className:"text-right font-medium",children:g(s.getValue("total_amount"))}),enablePinning:!0,enableHiding:!1}]}function Ue({columns:a,data:s,showPagination:t=!1,pageSize:n=10}){var M;const[m,r]=h.useState({}),[i,u]=h.useState({}),[c,d]=h.useState([]),[p,x]=h.useState([]),[N,b]=h.useState({pageIndex:0,pageSize:n}),_=De({data:s,columns:a,state:{sorting:p,columnVisibility:i,rowSelection:m,columnFilters:c,pagination:N},enableRowSelection:!0,enableColumnPinning:!0,onRowSelectionChange:r,onSortingChange:x,onColumnFiltersChange:d,onColumnVisibilityChange:u,onPaginationChange:b,getCoreRowModel:Le(),getFilteredRowModel:Ie(),getSortedRowModel:Ae(),getPaginationRowModel:Pe(),getFacetedRowModel:Fe(),getFacetedUniqueValues:ke()});return e.jsxs("div",{className:"w-full space-y-4",children:[e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsx("div",{className:"overflow-x-auto rounded-md border bg-white",style:{maxWidth:"calc(100vw - 2rem)",scrollbarWidth:"thin",scrollbarColor:"#cbd5e1 #f1f5f9"},children:e.jsxs(te,{style:{minWidth:"1200px"},children:[e.jsx(ze,{children:_.getHeaderGroups().map(f=>e.jsx(P,{children:f.headers.map(j=>e.jsx(Ee,{colSpan:j.colSpan,children:j.isPlaceholder?null:se(j.column.columnDef.header,j.getContext())},j.id))},f.id))}),e.jsx(ae,{children:(M=_.getRowModel().rows)!=null&&M.length?_.getRowModel().rows.map(f=>e.jsx(P,{"data-state":f.getIsSelected()&&"selected",children:f.getVisibleCells().map(j=>e.jsx(l,{children:se(j.column.columnDef.cell,j.getContext())},j.id))},f.id)):e.jsx(P,{children:e.jsx(l,{colSpan:a.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]})})}),t&&s.length>0&&e.jsx("div",{className:"mt-4",children:e.jsx(Ge,{table:_})})]})}function We({showEmployeeInfo:a=!0,showPaymentMethod:s=!0}){return e.jsx("div",{className:"rounded-md border",children:e.jsx(te,{children:e.jsx(ae,{children:Array.from({length:5}).map((t,n)=>e.jsxs(P,{children:[e.jsx(l,{children:e.jsx(o,{className:"h-4 w-8"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-24"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-16"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-8"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-32"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-16"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),s&&e.jsx(l,{children:e.jsx(o,{className:"h-4 w-16"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-16"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-16"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-16"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),a&&e.jsx(l,{children:e.jsx(o,{className:"h-4 w-24"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-24"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})}),e.jsx(l,{children:e.jsx(o,{className:"h-4 w-20"})})]},n))})})})}function qe({dateRange:a,selectedStores:s=["all-stores"],pageSize:t=20,className:n,showPagination:m=!0}){const{selectedBrand:r}=A(),[i,u]=h.useState(0);h.useEffect(()=>{const b=()=>{u(_=>_+1)};return window.addEventListener("brandChanged",b),()=>{window.removeEventListener("brandChanged",b)}},[]);const{data:c,totalAmount:d,isLoading:p,error:x}=Be({dateRange:a,selectedStores:s,autoFetch:!0}),N=h.useMemo(()=>c,[c,r==null?void 0:r.id,i]);return x?e.jsxs("div",{className:C("text-center text-red-500",n),children:["Lỗi khi tải dữ liệu: ",x]}):e.jsxs("div",{className:C("w-full max-w-full space-y-4 overflow-hidden",n),children:[e.jsx("div",{className:"space-y-2",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Bảng Kê Chi Tiết Hoá Đơn Bán Hàng"}),e.jsxs(ee,{variant:"outline",className:"text-xs",children:["Tổng: ",N.length," hoá đơn"]}),e.jsx(ee,{variant:"secondary",className:"text-xs",children:g(d)})]})})}),p&&e.jsx(We,{}),!p&&e.jsx(e.Fragment,{children:e.jsx(Ue,{columns:Oe(),data:N,showPagination:m,pageSize:t})})]})}function Ye(){const{selectedBrand:a}=A(),[s,t]=h.useState("daily"),[n,m]=h.useState(q()),[r,i]=h.useState(["all-stores"]),[u,c]=h.useState(["all-sources"]),d=x=>{t(x),x==="monthly"?m(ve()):x==="daily"&&m(q())},p=h.useMemo(()=>({dateRange:n,filterType:s,selectedStores:r,selectedSources:u,brandId:a==null?void 0:a.id}),[n,s,r,u,a==null?void 0:a.id]);return e.jsx(je.Provider,{value:p,children:e.jsxs("div",{className:"space-y-6",children:[e.jsx($e,{dateRange:n,onDateRangeChange:m,selectedStores:r,onStoreChange:i,selectedSources:u,onSourceChange:c,filterType:s,onFilterTypeChange:d}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsx(qe,{dateRange:n,selectedStores:r,sourceId:10000172,pageSize:10,showPagination:!0},a==null?void 0:a.id)})]})})}function Qe(){const{selectedBrand:a}=ce(),s=a==null?void 0:a.name,t=s?`Bảng Kê Chi Tiết Hoá Đơn Bán Hàng - ${s}`:"Bảng Kê Chi Tiết Hoá Đơn Bán Hàng";return e.jsxs(e.Fragment,{children:[e.jsxs(he,{children:[e.jsx(pe,{links:Je}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(be,{}),e.jsx(ye,{}),e.jsx(Ne,{})]})]}),e.jsxs(ue,{children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:t})}),e.jsxs(me,{orientation:"vertical",defaultValue:"overview",className:"space-y-4",children:[e.jsx("div",{className:"w-full overflow-x-auto pb-2",children:e.jsxs(de,{children:[e.jsx(K,{value:"overview",children:"Tổng Quan"}),e.jsx(K,{value:"stores",children:"Theo Cửa Hàng"}),e.jsx(K,{value:"products",children:"Theo Sản Phẩm"}),e.jsx(K,{value:"time",children:"Theo Thời Gian"})]})}),e.jsx(D,{value:"overview",className:"space-y-4",children:e.jsx(Ye,{})}),e.jsx(D,{value:"stores",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo cửa hàng"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(D,{value:"products",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo sản phẩm"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(D,{value:"time",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo thời gian"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})})]})]})]})}const Je=[{title:"Bảng Kê Chi Tiết",href:"/bao-cao/ke-toan/hoa-don",isActive:!0,disabled:!1},{title:"Theo Cửa Hàng",href:"/bao-cao/ke-toan/hoa-don/cua-hang",isActive:!1,disabled:!0},{title:"Theo Sản Phẩm",href:"/bao-cao/ke-toan/hoa-don/san-pham",isActive:!1,disabled:!0},{title:"Theo Thời Gian",href:"/bao-cao/ke-toan/hoa-don/thoi-gian",isActive:!1,disabled:!0}],Bs=Qe;export{Bs as component};
