import{a3 as F,a4 as C,j as W,r as D,h as Y,b as z,l as Z}from"./index-sntk-7aJ.js";import{j as _}from"./date-utils-DBbLjCz0.js";import{u as E}from"./useQuery-CPo_FvE_.js";import{u as R}from"./useMutation-jqWRauQa.js";import{b as g}from"./pos-api-CQfNAror.js";import{Q as h}from"./query-keys-3lmd-xp6.js";const S=new Map,p=new Map,J=5*60*1e3,m={getServiceCharges:async e=>{const r=`${e.company_uid}-${e.brand_uid}-${e.page||1}-${e.list_store_uid||"all"}-${e.active??"all"}-${e.status||"all"}`,t=S.get(r);if(t&&Date.now()-t.timestamp<J)return t.data;const n=p.get(r);if(n)return n;const c=(async()=>{var u,b,l;try{const s=new URLSearchParams;s.set("company_uid",e.company_uid),s.set("brand_uid",e.brand_uid),e.page&&s.set("page",e.page.toString()),e.list_store_uid&&s.set("list_store_uid",e.list_store_uid),e.active!==void 0&&s.set("active",e.active.toString()),e.limit&&s.set("limit",e.limit.toString()),e.status&&s.set("status",e.status);const a=await g.get(`/mdata/v1/service-charges?${s.toString()}`);if(!a.data||typeof a.data!="object")throw new Error("Invalid response format from service charge API");const f=a.data;return S.set(r,{data:f,timestamp:Date.now()}),f}catch(s){throw console.error("Error fetching service charges:",s),((u=s.response)==null?void 0:u.status)===429?new Error("Too many requests - please wait a moment before trying again."):((b=s.response)==null?void 0:b.status)===401?new Error("Unauthorized - please check your authentication."):((l=s.response)==null?void 0:l.status)===403?new Error("Forbidden - you do not have permission to access this resource."):s}finally{p.delete(r)}})();return p.set(r,c),c},getServiceChargeDetail:async e=>{try{const r=new URLSearchParams;return r.set("company_uid",e.company_uid),r.set("brand_uid",e.brand_uid),r.set("id",e.id),(await g.get(`/mdata/v1/service-charge?${r.toString()}`)).data.data}catch(r){throw console.error("Error fetching service charge detail:",r),r}},createServiceCharge:async e=>{try{await g.post("/mdata/v1/service-charge",e),S.clear()}catch(r){throw console.error("Error creating service charge:",r),r}},updateServiceCharge:async e=>{try{await g.put("/mdata/v1/service-charge",e),S.clear()}catch(r){throw console.error("Error updating service charge:",r),r}},deleteServiceCharge:async e=>{try{const r=new URLSearchParams;r.set("company_uid",e.company_uid),r.set("brand_uid",e.brand_uid),r.set("id",e.id),await g.delete(`/mdata/v1/service-charges?${r.toString()}`),S.clear()}catch(r){throw console.error("Error deleting service charge:",r),r}},getAreaNames:async e=>{try{const r=new URLSearchParams;return r.set("skip_limit","true"),r.set("company_uid",e.company_uid),r.set("brand_uid",e.brand_uid),r.set("store_uid",e.store_uid),r.set("list_area_id",e.list_area_id),(await g.get(`/pos/v1/area/get-name?${r.toString()}`)).data}catch(r){throw console.error("Error fetching area names:",r),r}},getSourceNames:async e=>{try{const r=new URLSearchParams;return r.set("skip_limit","true"),r.set("company_uid",e.company_uid),r.set("brand_uid",e.brand_uid),r.set("store_uid",e.store_uid),r.set("list_source_id",e.list_source_id),(await g.get(`/mdata/v1/sources?${r.toString()}`)).data}catch(r){throw console.error("Error fetching source names:",r),r}},getServiceChargePrograms:async e=>{var r;try{const t=new URLSearchParams;return t.set("skip_limit","true"),t.set("company_uid",e.company_uid),t.set("brand_uid",e.brand_uid),t.set("store_uid",e.store_uid),((r=(await g.get(`/mdata/v1/service-charges?${t.toString()}`)).data)==null?void 0:r.data)||[]}catch(t){throw console.error("Error fetching service charge programs:",t),t}},cloneServiceCharges:async e=>{try{const r={company_uid:e.company_uid,brand_uid:e.brand_uid,list_service_charge_uid:e.list_service_charge_uid,list_store_uid_target:e.list_store_uid_target};return(await g.post("/mdata/v1/clone-service-charge",r)).data}catch(r){throw console.error("Error cloning service charges:",r),r}},clearCache:()=>{S.clear(),p.clear()}},de=(e={})=>{const{params:r={},enabled:t=!0}=e;return E({queryKey:[h.SERVICE_CHARGE,r],queryFn:async()=>{if(!r.company_uid||!r.brand_uid)throw new Error("Company UID and Brand UID are required");return(await m.getServiceCharges(r)).data||[]},enabled:t&&!!(r.company_uid&&r.brand_uid),staleTime:5*60*1e3,refetchInterval:10*60*1e3})},X=e=>E({queryKey:[h.SERVICE_CHARGE,"detail",e.company_uid,e.brand_uid,e.id],queryFn:async()=>{if(!e.company_uid||!e.brand_uid||!e.id)throw new Error("Missing required parameters for service charge detail");return m.getServiceChargeDetail({company_uid:e.company_uid,brand_uid:e.brand_uid,id:e.id})},enabled:e.enabled&&!!(e.company_uid&&e.brand_uid&&e.id),staleTime:2*60*1e3,gcTime:5*60*1e3}),ee=e=>{const r=F();return R({mutationFn:t=>m.createServiceCharge(t),onSuccess:()=>{var t;r.invalidateQueries({queryKey:[h.SERVICE_CHARGE]}),C.success("Đã tạo phí dịch vụ thành công!"),(t=e==null?void 0:e.onSuccess)==null||t.call(e)},onError:t=>{console.error("Error creating service charge:",t),C.error(t.message||"Lỗi khi tạo phí dịch vụ")}})},re=e=>{const r=F();return R({mutationFn:t=>m.updateServiceCharge(t),onSuccess:()=>{var t;r.invalidateQueries({queryKey:[h.SERVICE_CHARGE]}),C.success("Đã cập nhật phí dịch vụ thành công!"),(t=e==null?void 0:e.onSuccess)==null||t.call(e)},onError:t=>{console.error("Error updating service charge:",t),C.error(t.message||"Lỗi khi cập nhật phí dịch vụ")}})},_e=(e,r=!0)=>E({queryKey:[h.SERVICE_CHARGE,"area-names",e],queryFn:async()=>e.list_area_id?await m.getAreaNames(e):{data:[]},enabled:r&&!!(e.company_uid&&e.brand_uid&&e.store_uid&&e.list_area_id),staleTime:10*60*1e3}),ge=(e,r=!0)=>E({queryKey:[h.SERVICE_CHARGE,"source-names",e],queryFn:async()=>e.list_source_id?await m.getSourceNames(e):{data:[]},enabled:r&&!!(e.company_uid&&e.brand_uid&&e.store_uid&&e.list_source_id),staleTime:10*60*1e3}),he=e=>E({queryKey:[h.SERVICE_CHARGE,"programs",e.company_uid,e.brand_uid,e.store_uid],queryFn:async()=>{if(!e.company_uid||!e.brand_uid||!e.store_uid)throw new Error("Missing required parameters");return m.getServiceChargePrograms({company_uid:e.company_uid,brand_uid:e.brand_uid,store_uid:e.store_uid})},enabled:e.enabled&&!!(e.company_uid&&e.brand_uid&&e.store_uid),staleTime:2*60*1e3,gcTime:5*60*1e3}),le=()=>{const e=F();return R({mutationFn:m.cloneServiceCharges,onSuccess:()=>{e.invalidateQueries({queryKey:[h.SERVICE_CHARGE]}),e.invalidateQueries({queryKey:[h.SERVICE_CHARGE,"programs"]}),C.success("Đã sao chép phí dịch vụ thành công!")},onError:r=>{console.error("Error cloning service charges:",r),C.error(r.message||"Lỗi khi sao chép phí dịch vụ")}})},I=D.createContext(void 0);function me({children:e,value:r}){return W.jsx(I.Provider,{value:r,children:e})}function P(){const e=D.useContext(I);if(e===void 0)throw new Error("useServiceChargeFormContext must be used within a ServiceChargeFormProvider");return e}function ye(){const{formData:e,updateFormData:r}=P();return{formData:e,updateFormData:r}}function ve(){const{handleBack:e,handleSave:r}=P();return{handleBack:e,handleSave:r}}function Se(){const{isFormValid:e,isLoading:r,isEditMode:t}=P();return{isFormValid:e,isLoading:r,isEditMode:t}}const te={storeUid:"",fromAmount:0,toAmount:0,isServiceChargeVoucher:!1,requiresQuantityInput:!1,chargeType:"PERCENT",chargeValue:0,startDate:new Date().toISOString().split("T")[0],endDate:new Date().toISOString().split("T")[0],marketingDays:[],marketingHours:[],active:1,filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:""},description:"",isUpdateSameServiceCharges:!1},ae=7*60*60*1e3;function Ce({serviceChargeId:e,initialStoreUid:r}={}){const t=Y(),{company:n}=z(),{selectedBrand:c}=Z(),u=!!e,b=u&&!!(n!=null&&n.id)&&!!(c!=null&&c.id)&&!!e,{data:l,isLoading:s}=X({company_uid:n==null?void 0:n.id,brand_uid:c==null?void 0:c.id,id:e,enabled:b}),[a,f]=D.useState({...te,storeUid:r||""}),U=i=>{const o=new Date(i);return new Date(o.getTime()+ae).toISOString().split("T")[0]},w=i=>new Date(i+"T00:00:00+07:00"),V=i=>{const o=U(i.from_date),d=U(i.to_date),y=_.convertNumbersToDayStrings(_.convertBitFlagsToDays(i.time_sale_date_week)),T=_.convertNumbersToHourStrings(_.convertBitFlagsToHours(i.time_sale_hour_day)),B=i.charge_type==="PERCENT"?Math.round((i.service_charge||0)*100*100)/100:i.service_charge||0,v=i.extra_data||{};return{storeUid:i.store_uid,fromAmount:i.from_amount||0,toAmount:i.to_amount||0,isServiceChargeVoucher:!1,requiresQuantityInput:!1,chargeType:i.charge_type,chargeValue:B,startDate:o,endDate:d,marketingDays:y,marketingHours:T,active:i.active,filterState:{is_all:0,is_item:0,is_type:0,type_id:"",item_id:"",is_combo:0,combo_id:"",is_area:v.is_area||0,area_ids:v.area_ids||[],is_table:v.is_table||0,table_ids:v.table_ids||[],is_source:v.is_source||0,source_ids:v.source_ids||[]},description:v.desciption||i.description||"",isUpdateSameServiceCharges:!1}};D.useEffect(()=>{if(l&&u){const i=V(l);f(i)}},[l,u]);const A=()=>{t({to:"/sale/service-charge"})},{mutate:k,isPending:x}=re({onSuccess:A}),{mutate:N,isPending:L}=ee({onSuccess:A}),M=A,$=()=>{const i=a.marketingDays.length>0?_.convertDayStringsToNumbers(a.marketingDays):[0,1,2,3,4,5,6],o=a.marketingHours.length>0?_.convertHourStringsToNumbers(a.marketingHours):_.getAllHours();return{timeSaleDateWeek:_.convertDaysToBitFlags(i),timeSaleHourDay:_.convertHoursToBitFlags(o)}},K=()=>a.chargeType==="PERCENT"?Math.round(a.chargeValue*100)/1e4:a.chargeValue,Q=()=>{var y,T,q;const i=((y=a.filterState)==null?void 0:y.source_ids)||[],o=((T=a.filterState)==null?void 0:T.area_ids)||[],d=((q=a.filterState)==null?void 0:q.table_ids)||[];return{sourceIds:i,areaIds:o,tableIds:d,extraData:{source_ids:i,area_ids:o,table_ids:d,is_source:i.length>0?1:0,is_area:o.length>0?1:0,is_table:d.length>0?1:0,desciption:a.description}}},G=()=>{const{timeSaleDateWeek:i,timeSaleHourDay:o}=$(),d=K(),{extraData:y}=Q();return u?l?{...l,charge_type:a.chargeType,service_charge:d.toString(),from_date:w(a.startDate).getTime(),to_date:w(a.endDate).getTime(),from_amount:a.fromAmount,to_amount:a.toAmount,time_sale_hour_day:o,time_sale_date_week:i,active:a.active,store_uid:a.storeUid,extra_data:y,is_update_same_service_charges:a.isUpdateSameServiceCharges}:{}:{store_uid:a.storeUid,charge_type:a.chargeType,from_amount:a.fromAmount,to_amount:a.toAmount,extra_data:y,from_date:w(a.startDate).getTime(),to_date:w(a.endDate).getTime(),time_sale_date_week:i,time_sale_hour_day:o,company_uid:(n==null?void 0:n.id)||"",brand_uid:(c==null?void 0:c.id)||"",service_charge:d.toString(),is_update_same_service_charges:a.isUpdateSameServiceCharges}},O=async()=>{if(!H)return;const i=G();(u?k:N)(i)},j=i=>{f(o=>({...o,...i}))},H=a.storeUid!==""&&a.chargeValue>0;return{formData:a,updateFormData:j,handleBack:M,handleSave:O,isFormValid:H,isLoading:L||x||s,isEditMode:u}}export{me as S,ge as a,le as b,he as c,re as d,de as e,ve as f,Se as g,ye as h,X as i,Ce as j,_e as u};
