import{a1 as Se,u as Te,l as Fe,a as Be,r as w,i as Oe,j as e,B as V,h as we,T as qe,o as Me,p as Ue,q as Ae}from"./index-sntk-7aJ.js";import{a as m,b as x,c as o,d as p,e as N,k as Ee,u as Le,F as Ke}from"./form-2rWd-Izg.js";import{s as Qe}from"./zod-DMJBVHr6.js";import{u as $e}from"./use-upload-image-FpCPKTWt.js";import{g as te,l as He,P as Ie,B as Xe,m as Ge,k as We,j as Ye,n as Je}from"./price-source-dialog-DzcYgTYK.js";import"./pos-api-CQfNAror.js";import"./user-B67nntxu.js";import"./vietqr-api-C-lVxzD-.js";import"./crm-api-DhFa_vPG.js";import{u as Ze}from"./use-item-types-DlwgINfg.js";import{u as De}from"./use-item-classes-DVU3n-Lf.js";import{u as Re}from"./use-units-DSbsiywC.js";import{u as es}from"./use-items-Dp-DpVBb.js";import{u as ss}from"./use-removed-items-DUNjx3Y6.js";import{u as ts}from"./use-customizations-Dvjv5zwl.js";import{u as is}from"./use-customization-by-id-lt4Zl_S3.js";import{u as ns}from"./use-sources-DVPy3MTi.js";import{u as ls}from"./useCanGoBack-xjQXkRLG.js";import{X as re}from"./calendar-BhUTNdpd.js";import{C as Q}from"./checkbox-gkM78Twn.js";import{I as q}from"./input-DCw8aMl6.js";import{T as as}from"./textarea-CUN2xTj-.js";import{C as X}from"./combobox-B4y2_kzV.js";import{D as cs,a as rs,b as os,c as ds}from"./dialog-HHE8oSxh.js";import{U as Ve}from"./upload-C8ni-I8t.js";import{C as ms,a as us,b as hs}from"./collapsible-DwXQUepC.js";import{C as xs}from"./confirm-dialog-CKbdgUwC.js";import"./header-CePLmjHC.js";import"./main-BTno3738.js";import"./search-context-iBulh32Z.js";import"./date-range-picker-CBqQlAZr.js";import"./multi-select-SpNKBlxk.js";import"./exceljs.min-C2daZjFR.js";import"./core.esm-DJUjDuYb.js";import{C as _s}from"./circle-help-DDGU4UNg.js";import{C as gs}from"./select-ZzLBlgJd.js";import{C as ps}from"./chevron-right-JsGDE6eB.js";function Pe({form:t}={}){var M;const _=t==null?void 0:t.watch("customization_uid"),{location:g}=Se(),j=((M=g.state)==null?void 0:M.store_uid)||(t==null?void 0:t.watch("store_uid")),{company:u}=Te(B=>B.auth),{selectedBrand:d}=Fe(),{stores:y}=Be(),{data:v=[]}=ns({company_uid:u==null?void 0:u.id,brand_uid:d==null?void 0:d.id,skip_limit:!0,enabled:!!(u!=null&&u.id)&&!!(d!=null&&d.id)}),{data:f=[]}=ts({skip_limit:!0,store_uid:j,enabled:!!j}),{data:C}=is(_||"",!!_&&_!=="none"),{data:I=[]}=es({params:{store_uid:j,skip_limit:!0},enabled:!!j}),{data:b=[]}=ss(),{data:z=[]}=Ze({store_uid:j,skip_limit:!0}),{data:T=[]}=Re(),{data:E=[]}=De({skip_limit:!0});return{selectedStoreUid:j,selectedCustomizationUid:_,company:u,selectedBrand:d,customizations:f,customizationDetails:C,items:I,sourcesData:v,cities:b,stores:y,itemTypes:z,units:T,itemClasses:E}}function js(){const[t,_]=w.useState(!1),[g,j]=w.useState(!1),[u,d]=w.useState(!1),[y,v]=w.useState(!1),[f,C]=w.useState(null);return{showQuantityInputs:t,isCustomizationDetailsOpen:g,isPriceSourceDialogOpen:u,isBuffetConfigModalOpen:y,confirmDeleteIndex:f,setShowQuantityInputs:_,setIsCustomizationDetailsOpen:j,setIsPriceSourceDialogOpen:d,setIsBuffetConfigModalOpen:v,setConfirmDeleteIndex:C,toggleQuantityInputs:()=>{_(!t)},openPriceSourceDialog:()=>{d(!0)},closePriceSourceDialog:()=>{d(!1)},openBuffetConfigModal:()=>{v(!0)},closeBuffetConfigModal:()=>{v(!1)},handleRemovePriceSource:h=>{C(h)},clearConfirmDelete:()=>{C(null)}}}function vs({isUpdate:t,currentRow:_,isLoading:g,onSave:j,onDeactive:u,onActive:d,isDeactivating:y=!1,isActivating:v=!1}){const f=Oe(),C=ls(),I=()=>{C?f.history.back():f.invalidate()},b=()=>t?"Chi tiết món":"Tạo món";return e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{children:e.jsx(V,{variant:"ghost",size:"sm",onClick:I,className:"flex items-center",children:e.jsx(re,{className:"h-4 w-4"})})}),e.jsx("h2",{className:"mb-2 text-3xl font-medium",children:b()}),e.jsxs("div",{className:"flex gap-2",children:[t&&(_==null?void 0:_.active)&&u&&e.jsx(V,{type:"button",variant:"outline",className:"border-red-500 text-red-500 hover:bg-red-50",disabled:y,onClick:u,children:y?"Đang deactive...":"Deactive"}),t&&!(_!=null&&_.active)&&d&&e.jsx(V,{type:"button",variant:"outline",className:"border-green-500 text-green-500 hover:bg-green-50",disabled:v,onClick:d,children:v?"Đang active...":"Active"}),e.jsx(V,{type:"button",disabled:g,onClick:j,children:g?"Đang lưu...":"Lưu"})]})]})})}const fs=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#F97316","#84CC16","#06B6D4","#6366F1"],bs=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9"];function Ns({open:t,onOpenChange:_,onImageSelect:g,onColorSelect:j,onSelfOrderToggle:u,selectedColor:d="#000000",useSelfOrderImage:y=!1,currentImageUrl:v}){const[f,C]=w.useState(null),[I,b]=w.useState(null),z=v||f,T=h=>{h||(b(null),C(null)),_(h)},E=h=>{var s;const l=(s=h.target.files)==null?void 0:s[0];if(l){b(l);const k=new FileReader;k.onload=L=>{var S;C((S=L.target)==null?void 0:S.result)},k.readAsDataURL(l)}},M=()=>{I?g(I):d&&d!=="#000000"&&j(d),T(!1)},B=h=>{j(h)};return e.jsx(cs,{open:t,onOpenChange:T,children:e.jsxs(rs,{className:"max-h-[90vh] w-[95vw] max-w-3xl overflow-y-auto lg:max-w-2xl",children:[e.jsx(os,{children:e.jsx(ds,{className:"text-lg font-semibold",children:"Chọn ảnh hoặc màu"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Chọn ảnh"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Bạn có thể chọn ảnh có kích thước 540x785px để sử dụng trên thiết bị Self Order"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Q,{checked:y,onCheckedChange:u}),e.jsx("label",{className:"text-sm font-medium",children:"Sử dụng ảnh cho thiết bị Self Order"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-4 transition-colors hover:bg-gray-100",style:{width:"467px",height:"420px",maxWidth:"100%",maxHeight:"380px"},onClick:()=>{var h;return(h=document.getElementById("dialog-image-upload"))==null?void 0:h.click()},children:z?e.jsx("img",{src:z,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"mx-auto mb-2 h-8 w-8 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Click để chọn ảnh"})]})}),e.jsx("input",{type:"file",accept:"image/*",onChange:E,className:"hidden",id:"dialog-image-upload"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Hoặc chọn màu dưới đây"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex space-x-2",children:fs.map(h=>e.jsx("button",{className:`h-8 w-8 rounded-full border-2 transition-all ${d===h?"scale-110 border-gray-900":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:h},onClick:()=>B(h)},h))}),e.jsx(q,{type:"text",value:d,onChange:h=>B(h.target.value),className:"w-24 text-center",placeholder:"#000000"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Khác"}),e.jsx("div",{className:"flex space-x-2",children:bs.map(h=>e.jsx("button",{className:`h-8 w-8 rounded-full border-2 transition-all ${d===h?"scale-110 border-gray-900":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:h},onClick:()=>B(h)},h))})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(V,{variant:"outline",onClick:()=>_(!1),children:"Huỷ"}),e.jsx(V,{onClick:M,children:"Xong"})]})]})})}function ys({form:t,mode:_,itemTypes:g,itemClasses:j,units:u,stores:d,onImageChange:y,imagePreview:v,onImageRemove:f}){const[C,I]=w.useState(!1),[b,z]=w.useState(()=>{const l=t.getValues("item_color");return l&&l.trim()!==""?l:"#000000"}),[T,E]=w.useState(!1),M=l=>{y({target:{files:[l]}}),z("#000000"),t.setValue("item_color","")},B=l=>{z(l),t.setValue("item_color",l),f&&f()},h=l=>{E(l)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Chi tiết"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-12",children:[e.jsxs("div",{className:"space-y-6 lg:col-span-9",children:[e.jsx(m,{control:t.control,name:"item_name",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Tên ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(p,{className:"flex-1",children:e.jsx(q,{placeholder:"Nhập tên món",className:"w-full",...l})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"ots_price",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Giá ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(p,{className:"flex-1",children:e.jsx(q,{placeholder:"0",className:"w-full",value:l.value?new Intl.NumberFormat("vi-VN").format(l.value):"",onChange:s=>{const k=s.target.value.replace(/[^\d]/g,"");l.onChange(k?Number(k):0)}})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"item_id",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mã món"}),e.jsx(p,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(q,{placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã món",className:"w-full",...l,readOnly:_==="update"||!t.watch("enable_custom_item_id")}),_==="create"&&e.jsx(m,{control:t.control,name:"enable_custom_item_id",render:({field:s})=>e.jsx(p,{children:e.jsx(Q,{checked:s.value,onCheckedChange:k=>{s.onChange(k),k?t.setValue("item_id","",{shouldDirty:!0,shouldValidate:!0}):t.setValue("item_id",te(),{shouldDirty:!0,shouldValidate:!0})},className:"border-2 border-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500"})})})]})})]}),e.jsx(N,{})]})})]}),e.jsx("div",{className:"lg:col-span-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-2 transition-colors hover:bg-gray-100",style:{height:"156px",backgroundColor:b&&b!=="#000000"?b:void 0},onClick:()=>I(!0),children:v?e.jsx("img",{src:v,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):b&&b!=="#000000"?e.jsx("div",{className:"h-full w-full rounded-lg",style:{backgroundColor:b}}):e.jsxs(e.Fragment,{children:[e.jsx(Ve,{className:"mb-1 h-6 w-6 text-gray-400"}),e.jsx("span",{className:"text-center text-xs text-gray-500",children:"Chọn ảnh"})]})}),(v||b&&b!=="#000000")&&e.jsx("button",{type:"button",onClick:()=>{v&&f&&f(),z("#000000"),t.setValue("item_color","")},className:"absolute -top-2 -right-2 rounded-full bg-gray-600 p-1 text-white transition-colors hover:bg-gray-700",children:e.jsx(re,{className:"h-3 w-3"})}),e.jsx("input",{type:"file",accept:"image/*",onChange:y,className:"hidden",id:"image-upload"})]})})]}),e.jsx(m,{control:t.control,name:"item_id_barcode",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mã barcode"}),e.jsx(p,{className:"flex-1",children:e.jsx(q,{placeholder:"Nếu bạn sử dụng tính năng scan QR thì POS hay tạo mã barcode",className:"w-full",maxLength:15,...l})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"is_eat_with",render:({field:l})=>e.jsx(x,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Món ăn kèm"}),e.jsx(p,{children:e.jsx(Q,{checked:l.value===1,onCheckedChange:s=>l.onChange(s?1:0)})})]})})}),e.jsx(m,{control:t.control,name:"no_update_quantity_toping",render:({field:l})=>e.jsx(x,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Không cập nhật số lượng món ăn kèm"}),e.jsx(p,{children:e.jsx(Q,{checked:l.value===1,onCheckedChange:s=>l.onChange(s?1:0)})})]})})}),e.jsx(m,{control:t.control,name:"item_type_uid",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Nhóm"}),e.jsx(p,{className:"flex-1",children:e.jsx(X,{options:g.map(s=>({value:s.id,label:s.item_type_name,disabled:s.active===0,status:s.active===0?"Deactive":void 0,statusClassName:s.active===0?"bg-red-100 text-red-700":void 0})),value:l.value,onValueChange:s=>l.onChange(s||void 0),placeholder:"Uncategory",searchPlaceholder:"Tìm kiếm...",emptyText:"Không tìm thấy nhóm.",className:"flex-1 text-blue-500"})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"item_class_uid",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Loại món"}),e.jsx(p,{className:"flex-1",children:e.jsx(X,{options:j.map(s=>({value:s.id,label:s.item_class_name,disabled:s.active===0,status:s.active===0?"Deactive":void 0,statusClassName:s.active===0?"bg-red-100 text-red-700":void 0})),value:l.value,onValueChange:s=>l.onChange(s||void 0),placeholder:"None",searchPlaceholder:"Tìm kiếm loại món...",emptyText:"Không tìm thấy loại món.",className:"flex-1 text-blue-500"})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"description",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mô tả"}),e.jsx(p,{className:"flex-1",children:e.jsx(as,{placeholder:"Nếu để trống thì tên món sẽ tự động làm mô tả món",className:"min-h-[80px] w-full",...l})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"apply_with_store",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Trạng thái"}),e.jsx(p,{className:"flex-1",children:e.jsx(q,{className:"w-full",value:Number(l.value)===1?"Sửa từ món gốc":"Món mới",readOnly:!0})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"store_uid",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Cửa hàng áp dụng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(p,{className:"flex-1",children:e.jsx(X,{options:d.map(s=>({value:s.id,label:s.store_name,disabled:s.active===0,status:s.active===0?"Deactive":void 0,statusClassName:s.active===0?"bg-red-100 text-red-700":void 0})),value:l.value,onValueChange:s=>l.onChange(s||void 0),placeholder:"Chọn cửa hàng",searchPlaceholder:"Tìm cửa hàng...",emptyText:"Không tìm thấy cửa hàng.",className:"flex-1 text-blue-500",disabled:_==="update"||!!t.getValues("store_uid")})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"item_id_mapping",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"SKU"}),e.jsx(p,{className:"flex-1",children:e.jsx(q,{placeholder:"Nhập mã SKU",className:"w-full",maxLength:50,...l})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"unit_uid",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Đơn vị tính ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(p,{className:"flex-1",children:e.jsx(X,{options:u.map(s=>({value:s.id,label:s.unit_name||s.id})),value:l.value,onValueChange:s=>l.onChange(s||void 0),placeholder:"Món",searchPlaceholder:"Tìm kiếm đơn vị tính...",emptyText:"Không tìm thấy đơn vị tính.",className:"flex-1 text-blue-500"})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"unit_secondary_uid",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Đơn vị tính thứ 2"}),e.jsx(p,{className:"flex-1",children:e.jsx(X,{options:u.map(s=>({value:s.id,label:s.unit_name||s.id})),value:l.value,onValueChange:s=>l.onChange(s||void 0),placeholder:"Chọn đơn vị tính",searchPlaceholder:"Tìm kiếm đơn vị tính...",emptyText:"Không tìm thấy đơn vị tính.",className:"flex-1 text-blue-500"})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"ots_tax",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"VAT món ăn"}),e.jsx(p,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(q,{type:"number",placeholder:"0",className:"w-full",inputMode:"decimal",step:"0.01",value:l.value===null||l.value===void 0?"":String((Number(l.value)||0)*100),onChange:s=>{const k=s.target.value,L=k===""?void 0:Number(k);l.onChange(L===void 0||Number.isNaN(L)?0:L/100)}}),e.jsx("span",{children:"%"})]})})]}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"time_cooking",render:({field:l})=>e.jsxs(x,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(o,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Thời gian chế biến (phút)"}),e.jsx(p,{className:"flex-1",children:e.jsx(q,{type:"number",placeholder:"0",className:"w-full",value:l.value?Math.round(l.value/6e4):"",onChange:s=>{const k=s.target.value;l.onChange(k?Number(k)*6e4:0)}})})]}),e.jsx(N,{})]})})]})]}),e.jsx(Ns,{open:C,onOpenChange:I,onImageSelect:M,onColorSelect:B,onSelfOrderToggle:h,selectedColor:b,useSelfOrderImage:T,currentImageUrl:v||void 0})]})}function Cs({form:t}){var R,ee;const _=we(),[g,j,u,d,y,v]=Ee({control:t.control,name:["is_virtual_item","is_buffet_item","cross_price","price_by_source","exclude_items_buffet","up_size_buffet"]}),{selectedCustomizationUid:f,customizations:C,customizationDetails:I,items:b,sourcesData:z}=Pe({form:t}),{showQuantityInputs:T,isCustomizationDetailsOpen:E,isPriceSourceDialogOpen:M,isBuffetConfigModalOpen:B,setIsCustomizationDetailsOpen:h,setIsPriceSourceDialogOpen:l,setIsBuffetConfigModalOpen:s,openPriceSourceDialog:k,openBuffetConfigModal:L,toggleQuantityInputs:S,handleRemovePriceSource:U,clearConfirmDelete:O,confirmDeleteIndex:G}=js(),[Z,D]=w.useState(null),[ie,W]=w.useState(null),ne=n=>{const a=t.getValues("price_by_source")||[];if(Z!==null){const c=[...a];c[Z]={source_id:n.source_id,price:n.price,source_name:n.source_name,price_times:n.price_times,is_source_exist_in_city:n.is_source_exist_in_city},t.setValue("price_by_source",c),D(null),W(null)}else{const c=[...a,{source_id:n.source_id,price:n.price,source_name:n.source_name,price_times:n.price_times,is_source_exist_in_city:n.is_source_exist_in_city}];t.setValue("price_by_source",c)}},Y=(n,a)=>{D(a),W(n),l(!0)},le=n=>{t.setValue("exclude_items_buffet",n)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình sửa giá, nhập số lượng, bỏ món"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Cho phép sửa giá khi bán"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"enable_edit_price",render:({field:n})=>e.jsx(x,{children:e.jsx(p,{children:e.jsx(Q,{checked:(n.value&2)===2,onCheckedChange:a=>{const c=a?n.value|2:n.value&-3;n.onChange(c)}})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Yêu cầu nhập số lượng khi gọi món"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"enable_edit_price",render:({field:n})=>e.jsx(x,{children:e.jsx(p,{children:e.jsx(Q,{checked:(n.value&4)===4,onCheckedChange:a=>{const c=a?n.value|4:n.value&-5;n.onChange(c)}})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-left font-medium text-gray-700",children:"Cho phép bỏ món mà không cần quyền áp dụng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"enable_edit_price",render:({field:n})=>e.jsx(x,{children:e.jsx(p,{children:e.jsx(Q,{checked:(n.value&8)===8,onCheckedChange:a=>{const c=a?n.value|8:n.value&-9;n.onChange(c)}})})})})})]})]}),e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Cấu hình món ảo"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"is_virtual_item",render:({field:n})=>e.jsx(x,{children:e.jsx(p,{children:e.jsx(Q,{checked:!!n.value,onCheckedChange:a=>n.onChange(a?1:0)})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Cấu hình món ăn là vé buffet"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"is_buffet_item",render:({field:n})=>e.jsx(x,{children:e.jsx(p,{children:e.jsx(Q,{checked:!!n.value,onCheckedChange:a=>n.onChange(a?1:0)})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Công thức inQr cho máy pha trà"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"formula_qrcode",render:({field:n})=>e.jsxs(x,{children:[e.jsx(p,{children:e.jsx(q,{placeholder:"Nhập công thức InQR",...n})}),e.jsx(N,{})]})})})]}),j===1&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Danh sách món không đi kèm vé buffet"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(V,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600",onClick:L,children:[y.length," món"]})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Danh sách vé buffet được upsize"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(V,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600",onClick:()=>{},children:[v.length," món"]})})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình món dịch vụ"}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Cấu hình món dịch vụ"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"is_service",render:({field:n})=>e.jsx(x,{children:e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(p,{children:e.jsx(Q,{checked:!!n.value,onCheckedChange:a=>n.onChange(!!a)})})})})})})]}),e.jsxs("div",{className:"space-y-4",children:[t.watch("is_service")?e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Cấu hình giá thay đổi theo số lượng"}),e.jsx(qe,{children:e.jsxs(Me,{children:[e.jsx(Ue,{asChild:!0,children:e.jsx(_s,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Ae,{children:e.jsx("p",{children:"Nếu khai báo cấu hình đổi giá theo số lượng thì món sẽ ko tự động áp dụng giá và giảm giá theo thời gian khi chạy của món dịch vụ nữa, chỉ được chọn 1 trong 2"})})]})})]}),e.jsx(V,{type:"button",size:"sm",variant:T||u.length>0?"outline":"default",className:T||u.length>0?"":"bg-blue-500 hover:bg-blue-600",onClick:S,children:T||u.length>0?"Xoá":"Thêm"})]}),(T||u.length>0)&&e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Khai báo số lượng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"quantity",render:({field:n})=>e.jsxs(x,{children:[e.jsx(p,{children:e.jsx(q,{type:"number",placeholder:"1",className:"bg-white",...n,value:n.value||"",onChange:a=>{const c=Number(a.target.value)||0;n.onChange(c);const r=t.getValues("price")||0;t.setValue("cross_price",[{quantity:c,price:r}])}})}),e.jsx(N,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Khai báo giá khi vượt qua số lượng trên"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"price",render:({field:n})=>e.jsxs(x,{children:[e.jsx(p,{children:e.jsx(q,{placeholder:"0",className:"bg-white",value:typeof n.value=="number"&&n.value>0?n.value.toLocaleString("vi-VN"):"",onChange:a=>{const c=a.target.value.replace(/\D/g,""),r=c?parseInt(c,10):0;n.onChange(r);const F=t.getValues("quantity")||0;t.setValue("cross_price",[{quantity:F,price:r}])}})}),e.jsx(N,{})]})})})]})]})})]}):null,!g&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình giá theo nguồn"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(o,{className:"font-medium text-gray-700",children:"Cấu hình giá theo nguồn"}),e.jsx(V,{type:"button",size:"sm",className:"bg-blue-500 hover:bg-blue-600",onClick:k,children:"Thêm nguồn"})]}),d.length>0&&e.jsx("div",{className:"space-y-2",children:d.map((n,a)=>e.jsxs("div",{className:"flex cursor-pointer items-center justify-between rounded-md border p-3 hover:bg-gray-50",onClick:()=>Y(n,a),children:[e.jsx("div",{children:e.jsxs("span",{className:"font-medium",children:[He(n.source_id,z)||n.sourceName," - Số tiền:"," ",n.price.toLocaleString()," ₫"]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{type:"button",variant:"ghost",size:"sm",onClick:c=>{c.stopPropagation(),Y(n,a)},className:"text-blue-600 hover:text-blue-700",children:"Sửa"}),e.jsx(V,{type:"button",variant:"ghost",size:"sm",onClick:c=>{c.stopPropagation(),U(a)},className:"text-gray-400 hover:text-gray-600",children:e.jsx(re,{className:"h-4 w-4"})})]})]},a))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Customization"}),e.jsx(V,{type:"button",size:"sm",className:"bg-blue-500 hover:bg-blue-600",onClick:()=>_({to:"/menu/customization/customization-in-store/detail"}),children:"Tạo customization"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Customization"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"customization_uid",render:({field:n})=>e.jsxs(x,{children:[e.jsx(X,{options:[{value:"none",label:"Không có"},...C.map(a=>({value:a.id,label:a.name}))],value:n.value??"none",onValueChange:a=>n.onChange(a==="none"?null:a),placeholder:"Chọn customization",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không tìm thấy customization.",className:"w-full"}),e.jsx(N,{})]})})})]}),f&&f!=="none"&&I&&e.jsx("div",{className:"space-y-4",children:e.jsxs(ms,{open:E,onOpenChange:h,children:[e.jsx(us,{asChild:!0,children:e.jsxs(V,{type:"button",variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsx("span",{className:"text-blue-600",children:"Chi tiết Customize"}),E?e.jsx(gs,{className:"h-4 w-4 text-blue-600"}):e.jsx(ps,{className:"h-4 w-4 text-blue-600"})]})}),e.jsx(hs,{className:"space-y-4",children:(ee=(R=I.data)==null?void 0:R.LstItem_Options)==null?void 0:ee.map(n=>{const a=n.LstItem_Id.map(c=>{const r=b.find(F=>F.item_id===c);return{id:(r==null?void 0:r.id)||c,name:(r==null?void 0:r.item_name)||c,price:(r==null?void 0:r.ots_price)||0,code:c,active:(r==null?void 0:r.active)??1}});return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:n.Name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(Chọn từ ",n.Min_Permitted," đến ",n.Max_Permitted," món)"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:a.map(c=>{const r=c.active===1;return e.jsxs("div",{className:`rounded-md border p-3 text-center ${r?"bg-gray-50":"cursor-not-allowed bg-gray-100 opacity-50"}`,children:[e.jsx("p",{className:`text-sm font-medium ${r?"":"text-gray-400"}`,children:c.name}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["(",c.code,")"]}),e.jsxs("p",{className:`mt-1 text-sm font-medium ${r?"text-green-600":"text-gray-400"}`,children:[c.price.toLocaleString("vi-VN",{minimumFractionDigits:0,maximumFractionDigits:0})," ","₫"]})]},c.id)})})]},n.id)})})]})})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(o,{className:"text-sm font-medium text-gray-700",children:"Khung thời gian bán"}),e.jsx(m,{control:t.control,name:"time_sale_date_week",render:({field:n})=>e.jsxs(x,{children:[e.jsx(o,{className:"text-sm text-gray-600",children:"Chọn ngày"}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:[{name:"Thứ 2",bit:4},{name:"Thứ 3",bit:8},{name:"Thứ 4",bit:16},{name:"Thứ 5",bit:32},{name:"Thứ 6",bit:64},{name:"Thứ 7",bit:128},{name:"Chủ nhật",bit:2}].map(({name:a,bit:c})=>{const r=c,F=(n.value&r)!==0;return e.jsx(V,{type:"button",variant:F?"default":"outline",size:"sm",className:`text-xs ${F?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const $=n.value||0,H=F?$&~r:$|r;n.onChange(H)},children:a},a)})}),e.jsx(N,{})]})}),e.jsx(m,{control:t.control,name:"time_sale_hour_day",render:({field:n})=>e.jsxs(x,{children:[e.jsx(o,{className:"text-sm text-gray-600",children:"Chọn giờ"}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(a,c)=>({hour:c,label:`${c}h`})).map(({hour:a,label:c})=>{const r=1<<a,F=(n.value&r)!==0;return e.jsx(V,{type:"button",variant:F?"default":"outline",size:"sm",className:`text-xs ${F?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const $=n.value||0,H=F?$&~r:$|r;n.onChange(H)},children:c},a)})}),e.jsx(N,{})]})})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(o,{className:"text-right font-medium text-gray-700",children:"Thứ tự hiển thị"}),e.jsx("div",{className:"col-span-2",children:e.jsx(m,{control:t.control,name:"sort",render:({field:n})=>e.jsxs(x,{children:[e.jsx(p,{children:e.jsx(q,{type:"number",placeholder:"Nhập số thứ tự hiển thị",...n,onChange:a=>n.onChange(Number(a.target.value))})}),e.jsx(N,{})]})})})]})})]})]}),e.jsx(Ie,{open:M,onOpenChange:l,onConfirm:ne,sources:z,data:ie}),e.jsx(Xe,{itemsBuffet:y||[],open:B,onOpenChange:s,onItemsChange:le,items:b}),e.jsx(xs,{open:G!==null,onOpenChange:n=>!n&&O(),title:"Bạn có muốn bỏ cấu hình ?",desc:"",confirmText:"Xóa",cancelBtnText:"Hủy",handleConfirm:()=>{if(G===null)return;const a=(t.getValues("price_by_source")||[]).filter((c,r)=>r!==G);t.setValue("price_by_source",a),O()}})]})}function ks({form:t,mode:_,itemTypes:g,itemClasses:j,units:u,stores:d,onImageChange:y,imagePreview:v,onImageRemove:f}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx(ys,{form:t,mode:_,itemTypes:g,itemClasses:j,units:u,stores:d,onImageChange:y,imagePreview:v,onImageRemove:f}),e.jsx(Cs,{form:t})]})}const Ss=Je;function dt({currentRow:t,isCopyMode:_=!1}){var c,r,F,$,H,oe,de,me,ue,he,xe,_e,ge,pe,je,ve,fe,be,Ne,ye,Ce;const g=!!t&&!_,j=!!t&&_,u=we(),[d,y]=w.useState(!1),[v,f]=w.useState(null),[C,I]=w.useState(null),{createItemAsync:b,isPending:z}=Ge(),{updateItemAsync:T,isPending:E}=We(),{updateStatusAsync:M,isPending:B}=Ye(),{uploadImage:h,isUploading:l}=$e(),s=t,{location:k}=Se(),L=(c=k.state)==null?void 0:c.store_uid;w.useEffect(()=>{s!=null&&s.image_path&&f(s.image_path)},[s==null?void 0:s.image_path,j]);const S=Le({resolver:Qe(Ss),defaultValues:{item_name:(s==null?void 0:s.item_name)||"",ots_price:Number(s==null?void 0:s.ots_price)||0,description:(s==null?void 0:s.description)||"",item_id_barcode:j?"":(s==null?void 0:s.item_id_barcode)||"",is_eat_with:Number(s==null?void 0:s.is_eat_with)||0,item_class_uid:(s==null?void 0:s.item_class_uid)||"",item_type_uid:(s==null?void 0:s.item_type_uid)||"",store_uid:((F=(r=s==null?void 0:s.Stores)==null?void 0:r[0])==null?void 0:F.id)||"",city_uid:((H=($=s==null?void 0:s.Stores)==null?void 0:$[0])==null?void 0:H.city_uid)||"",item_id:j?"":(s==null?void 0:s.item_id)||"",enable_custom_item_id:g&&s!=null&&s.item_id&&(s==null?void 0:s.item_id).trim()!==""?1:0,unit_uid:(s==null?void 0:s.unit_uid)||"",unit_secondary_uid:(s==null?void 0:s.unit_secondary_uid)||"",ots_tax:Number(s==null?void 0:s.ots_tax)||0,time_cooking:Number(s==null?void 0:s.time_cooking)||0,ta_price:Number(s==null?void 0:s.ta_price)||0,ta_tax:Number(s==null?void 0:s.ta_tax)||0,item_id_mapping:(s==null?void 0:s.item_id_mapping)||"",enable_edit_price:Number((oe=s==null?void 0:s.extra_data)==null?void 0:oe.enable_edit_price)||0,is_print_label:Number(s==null?void 0:s.is_print_label)||0,is_allow_discount:Number(s==null?void 0:s.is_allow_discount)||0,is_virtual_item:Number((de=s==null?void 0:s.extra_data)==null?void 0:de.is_virtual_item)||0,is_item_service:Number((me=s==null?void 0:s.extra_data)==null?void 0:me.is_item_service)||0,is_buffet_item:Number((ue=s==null?void 0:s.extra_data)==null?void 0:ue.is_buffet_item)||0,no_update_quantity_toping:Number((he=s==null?void 0:s.extra_data)==null?void 0:he.no_update_quantity_toping)||0,formula_qrcode:((xe=s==null?void 0:s.extra_data)==null?void 0:xe.formula_qrcode)||"",is_service:Number(s==null?void 0:s.is_service)||0,price_by_source:((_e=s==null?void 0:s.extra_data)==null?void 0:_e.price_by_source)||[],exclude_items_buffet:((ge=s==null?void 0:s.extra_data)==null?void 0:ge.exclude_items_buffet)||[],up_size_buffet:((pe=s==null?void 0:s.extra_data)==null?void 0:pe.up_size_buffet)||[],cross_price:((je=s==null?void 0:s.extra_data)==null?void 0:je.cross_price)||[],quantity:((be=(fe=(ve=s==null?void 0:s.extra_data)==null?void 0:ve.cross_price)==null?void 0:fe[0])==null?void 0:be.quantity)||0,price:((Ce=(ye=(Ne=s==null?void 0:s.extra_data)==null?void 0:Ne.cross_price)==null?void 0:ye[0])==null?void 0:Ce.price)||0,time_sale_date_week:Number(s==null?void 0:s.time_sale_date_week)||0,time_sale_hour_day:Number(s==null?void 0:s.time_sale_hour_day)||0,sort:Number(s==null?void 0:s.sort)||0,customization_uid:(s==null?void 0:s.customization_uid)||null,apply_with_store:Number(s==null?void 0:s.apply_with_store)||2,item_color:(s==null?void 0:s.item_color)||""}}),{company:U,selectedBrand:O,sourcesData:G,itemTypes:Z,units:D,itemClasses:ie,stores:W}=Pe({form:S});w.useEffect(()=>{if(g||!L)return;const i=S.getValues("store_uid");(!i||String(i).trim()==="")&&S.setValue("store_uid",L,{shouldDirty:!0,shouldValidate:!0})},[g,L]);const ne=i=>{var K;const A=(K=i.target.files)==null?void 0:K[0];if(A){I(A);const J=new FileReader;J.onload=ae=>{var se;f((se=ae.target)==null?void 0:se.result)},J.readAsDataURL(A)}},Y=async i=>{if(!(!(U!=null&&U.id)||!(O!=null&&O.id)))try{let A="",K="",J=i.item_color||"";if(C){const P=await h(C);P&&(A=P.image_path,K=P.image_path_thumb,J="")}else v&&!j&&(A=(s==null?void 0:s.image_path)||"",K=(s==null?void 0:s.image_path_thumb)||"");const ae={price_by_source:(i.price_by_source||[]).map(P=>({price:Number(P.price??P.amount??0),source_id:String(P.source_id??P.source??P.sourceId??""),price_times:Array.isArray(P.price_times)?P.price_times:[],is_source_exist_in_city:P.is_source_exist_in_city??!0})),is_virtual_item:i.is_virtual_item,is_item_service:i.is_item_service,no_update_quantity_toping:i.no_update_quantity_toping,enable_edit_price:i.enable_edit_price,is_buffet_item:i.is_buffet_item,exclude_items_buffet:i.exclude_items_buffet||[],up_size_buffet:i.up_size_buffet||[],cross_price:i.cross_price||[],formula_qrcode:i.formula_qrcode||""},se=g?i.enable_custom_item_id&&i.item_id?i.item_id:(s==null?void 0:s.item_id)||(t==null?void 0:t.item_id)||"":j?i.enable_custom_item_id&&i.item_id&&i.item_id.trim()!==""?i.item_id:te():i.enable_custom_item_id&&i.item_id?i.item_id:te(),ce=(W||[]).find(P=>String(P.id)===String(i.store_uid)||String(P.store_uid)===String(i.store_uid)),ze={item_id:se,item_name:i.item_name,description:i.description||"",ots_price:i.ots_price,ots_tax:i.ots_tax,ta_price:i.ta_price,ta_tax:i.ta_tax,time_sale_hour_day:i.time_sale_hour_day,time_sale_date_week:i.time_sale_date_week,allow_take_away:i.allow_take_away,is_eat_with:i.is_eat_with?1:0,image_path:A,image_path_thumb:K,item_color:J,list_order:i.list_order,is_service:i.is_service?1:0,is_material:i.is_material,active:1,user_id:(s==null?void 0:s.user_id)||"",is_foreign:0,quantity_default:i.quantity_default,price_change:i.price_change,currency_type_id:(s==null?void 0:s.currency_type_id)||"",point:i.point,is_gift:i.is_gift,is_fc:i.is_fc,show_on_web:i.show_on_web,show_price_on_web:i.show_price_on_web,cost_price:i.cost_price,is_print_label:i.is_print_label?1:0,quantity_limit:i.quantity_limit,is_kit:i.is_kit,process_index:i.process_index,quantity_per_day:i.quantity_per_day,is_parent:i.is_parent,is_sub:i.is_sub,effective_date:i.effective_date,expire_date:i.expire_date,time_cooking:i.time_cooking,item_id_barcode:i.item_id_barcode||"",is_allow_discount:i.is_allow_discount?1:0,item_id_eat_with:(s==null?void 0:s.item_id_eat_with)||"",item_id_mapping:i.item_id_mapping||"",unit_uid:i.unit_uid,unit_secondary_uid:i.unit_secondary_uid||null,item_class_uid:i.item_class_uid||null,item_type_uid:i.item_type_uid,city_uid:(ce==null?void 0:ce.city_uid)||"",store_uid:i.store_uid||"",sort:i.sort,company_uid:U.id,brand_uid:O.id,sort_online:i.sort_online,customization_uid:i.customization_uid||null,is_fabi:1,apply_with_store:i.apply_with_store,revision:0,extra_data:ae},ke={...g?{id:(t==null?void 0:t.id)||""}:{},...ze};g&&(t!=null&&t.id)?(await T(ke),u({to:"/menu/items/items-in-store"})):(await b(ke),u({to:"/menu/items/items-in-store"}))}catch(A){console.error("Error submitting form:",A)}},le=async()=>{if(!(!(t!=null&&t.id)||!(U!=null&&U.id)||!(O!=null&&O.id)))try{await M({id:t.id,active:0}),u({to:"/menu/items/items-in-store"})}catch{}},R=async()=>{if(!(!(t!=null&&t.id)||!(U!=null&&U.id)||!(O!=null&&O.id)))try{await M({id:t.id,active:1}),u({to:"/menu/items/items-in-store"})}catch{}},ee=async()=>{if(!g){const A=S.getValues("enable_custom_item_id"),K=S.getValues("item_id");!A&&(!K||String(K).trim()==="")&&S.setValue("item_id",te(),{shouldDirty:!0})}await S.trigger()&&S.handleSubmit(Y)()},n=i=>{const K=[...S.getValues("price_by_source")||[],{source_id:i.source_id,price:i.price,source_name:i.source_name,price_times:Array.isArray(i.price_times)?i.price_times:[],is_source_exist_in_city:i.is_source_exist_in_city??!0}];S.setValue("price_by_source",K,{shouldDirty:!0,shouldValidate:!0}),y(!1)},a=z||E||l;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(vs,{isUpdate:g,isCopy:j,currentRow:t,isLoading:a,onSave:ee,onDeactive:g?le:void 0,onActive:g?R:void 0,isDeactivating:B,isActivating:B}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"bg-white",children:e.jsx(Ke,{...S,children:e.jsx("form",{onSubmit:S.handleSubmit(Y),className:"space-y-6",children:e.jsx(ks,{form:S,mode:g?"update":"create",itemTypes:Z,itemClasses:ie,units:D,stores:W,imageFile:C,onImageChange:ne,imagePreview:v,onImageRemove:()=>{f(null),I(null)}})})})})})]}),e.jsx(Ie,{open:d,onOpenChange:y,onConfirm:n,sources:G})]})}export{dt as I};
