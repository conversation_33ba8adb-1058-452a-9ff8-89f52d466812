import{R as d}from"./index-sntk-7aJ.js";import{I as p,J as M,K,n as g,M as J,N as Q,i as F,x as I,O as V,P as D,Q as T,U as B,f as U}from"./generateCategoricalChart-DOYdoRWE.js";var X=["x1","y1","x2","y2","key"],Y=["offset"];function O(t){"@babel/helpers - typeof";return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},O(t)}function W(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),r.push.apply(r,i)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?W(Object(r),!0).forEach(function(i){q(t,i,r[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(r,i))})}return t}function q(t,e,r){return e=Z(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Z(t){var e=R(t,"string");return O(e)=="symbol"?e:e+""}function R(t,e){if(O(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var i=r.call(t,e);if(O(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function b(){return b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},b.apply(this,arguments)}function H(t,e){if(t==null)return{};var r=tt(t,e),i,a;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(t);for(a=0;a<l.length;a++)i=l[a],!(e.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(t,i)&&(r[i]=t[i])}return r}function tt(t,e){if(t==null)return{};var r={};for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)){if(e.indexOf(i)>=0)continue;r[i]=t[i]}return r}var et=function(e){var r=e.fill;if(!r||r==="none")return null;var i=e.fillOpacity,a=e.x,l=e.y,o=e.width,u=e.height,s=e.ry;return d.createElement("rect",{x:a,y:l,ry:s,width:o,height:u,stroke:"none",fill:r,fillOpacity:i,className:"recharts-cartesian-grid-bg"})};function L(t,e){var r;if(d.isValidElement(t))r=d.cloneElement(t,e);else if(F(t))r=t(e);else{var i=e.x1,a=e.y1,l=e.x2,o=e.y2,u=e.key,s=H(e,X),c=U(s,!1);c.offset;var n=H(c,Y);r=d.createElement("line",b({},n,{x1:i,y1:a,x2:l,y2:o,fill:"none",key:u}))}return r}function it(t){var e=t.x,r=t.width,i=t.horizontal,a=i===void 0?!0:i,l=t.horizontalPoints;if(!a||!l||!l.length)return null;var o=l.map(function(u,s){var c=v(v({},t),{},{x1:e,y1:u,x2:e+r,y2:u,key:"line-".concat(s),index:s});return L(a,c)});return d.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function rt(t){var e=t.y,r=t.height,i=t.vertical,a=i===void 0?!0:i,l=t.verticalPoints;if(!a||!l||!l.length)return null;var o=l.map(function(u,s){var c=v(v({},t),{},{x1:u,y1:e,x2:u,y2:e+r,key:"line-".concat(s),index:s});return L(a,c)});return d.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function nt(t){var e=t.horizontalFill,r=t.fillOpacity,i=t.x,a=t.y,l=t.width,o=t.height,u=t.horizontalPoints,s=t.horizontal,c=s===void 0?!0:s;if(!c||!e||!e.length)return null;var n=u.map(function(h){return Math.round(h+a-a)}).sort(function(h,f){return h-f});a!==n[0]&&n.unshift(0);var m=n.map(function(h,f){var w=!n[f+1],y=w?a+o-h:n[f+1]-h;if(y<=0)return null;var x=f%e.length;return d.createElement("rect",{key:"react-".concat(f),y:h,x:i,height:y,width:l,stroke:"none",fill:e[x],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},m)}function at(t){var e=t.vertical,r=e===void 0?!0:e,i=t.verticalFill,a=t.fillOpacity,l=t.x,o=t.y,u=t.width,s=t.height,c=t.verticalPoints;if(!r||!i||!i.length)return null;var n=c.map(function(h){return Math.round(h+l-l)}).sort(function(h,f){return h-f});l!==n[0]&&n.unshift(0);var m=n.map(function(h,f){var w=!n[f+1],y=w?l+u-h:n[f+1]-h;if(y<=0)return null;var x=f%i.length;return d.createElement("rect",{key:"react-".concat(f),x:h,y:o,width:y,height:s,stroke:"none",fill:i[x],fillOpacity:a,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},m)}var lt=function(e,r){var i=e.xAxis,a=e.width,l=e.height,o=e.offset;return V(D(v(v(v({},B.defaultProps),i),{},{ticks:T(i,!0),viewBox:{x:0,y:0,width:a,height:l}})),o.left,o.left+o.width,r)},ot=function(e,r){var i=e.yAxis,a=e.width,l=e.height,o=e.offset;return V(D(v(v(v({},B.defaultProps),i),{},{ticks:T(i,!0),viewBox:{x:0,y:0,width:a,height:l}})),o.top,o.top+o.height,r)},P={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function ct(t){var e,r,i,a,l,o,u=p(),s=M(),c=K(),n=v(v({},t),{},{stroke:(e=t.stroke)!==null&&e!==void 0?e:P.stroke,fill:(r=t.fill)!==null&&r!==void 0?r:P.fill,horizontal:(i=t.horizontal)!==null&&i!==void 0?i:P.horizontal,horizontalFill:(a=t.horizontalFill)!==null&&a!==void 0?a:P.horizontalFill,vertical:(l=t.vertical)!==null&&l!==void 0?l:P.vertical,verticalFill:(o=t.verticalFill)!==null&&o!==void 0?o:P.verticalFill,x:g(t.x)?t.x:c.left,y:g(t.y)?t.y:c.top,width:g(t.width)?t.width:c.width,height:g(t.height)?t.height:c.height}),m=n.x,h=n.y,f=n.width,w=n.height,y=n.syncWithTicks,x=n.horizontalValues,S=n.verticalValues,z=J(),A=Q();if(!g(f)||f<=0||!g(w)||w<=0||!g(m)||m!==+m||!g(h)||h!==+h)return null;var G=n.verticalCoordinatesGenerator||lt,N=n.horizontalCoordinatesGenerator||ot,j=n.horizontalPoints,k=n.verticalPoints;if((!j||!j.length)&&F(N)){var _=x&&x.length,E=N({yAxis:A?v(v({},A),{},{ticks:_?x:A.ticks}):void 0,width:u,height:s,offset:c},_?!0:y);I(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(O(E),"]")),Array.isArray(E)&&(j=E)}if((!k||!k.length)&&F(G)){var $=S&&S.length,C=G({xAxis:z?v(v({},z),{},{ticks:$?S:z.ticks}):void 0,width:u,height:s,offset:c},$?!0:y);I(Array.isArray(C),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(O(C),"]")),Array.isArray(C)&&(k=C)}return d.createElement("g",{className:"recharts-cartesian-grid"},d.createElement(et,{fill:n.fill,fillOpacity:n.fillOpacity,x:n.x,y:n.y,width:n.width,height:n.height,ry:n.ry}),d.createElement(it,b({},n,{offset:c,horizontalPoints:j,xAxis:z,yAxis:A})),d.createElement(rt,b({},n,{offset:c,verticalPoints:k,xAxis:z,yAxis:A})),d.createElement(nt,b({},n,{horizontalPoints:j})),d.createElement(at,b({},n,{verticalPoints:k})))}ct.displayName="CartesianGrid";export{ct as C};
