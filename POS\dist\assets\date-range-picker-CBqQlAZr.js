import{r as D,j as g,c as E,T as wt,S as He,x as vt,o as St,p as Tt,q as Nt,B as se,R as ee}from"./index-sntk-7aJ.js";import{R as Ct,a as Dt,b as Mt,X as jt,T as Et,D as Pt,P as It,O as kt,C as Lt}from"./calendar-BhUTNdpd.js";import{c as At}from"./createLucideIcon-CvoWT756.js";import{a as Ot,C as Rt}from"./chevron-right-JsGDE6eB.js";import{j as zt,h as Ht,f as Bt}from"./react-icons.esm-BTYMKzFL.js";import{P as Vt,a as _t,b as Ft}from"./popover-DUo0D-5L.js";import{S as Wt,a as qt,b as $t,c as Kt,d as Qt}from"./select-ZzLBlgJd.js";import{b as Le,a as me,c as ge,d as Gt}from"./isSameMonth-C8JQo-AN.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xt=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Yt=At("panel-left",Xt),Ae=768;function Ut(){const[e,t]=D.useState(void 0);return D.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Ae-1}px)`),r=()=>{t(window.innerWidth<Ae)};return n.addEventListener("change",r),t(window.innerWidth<Ae),()=>n.removeEventListener("change",r)},[]),!!e}function Jt({...e}){return g.jsx(Ct,{"data-slot":"sheet",...e})}function Zt({...e}){return g.jsx(It,{"data-slot":"sheet-portal",...e})}function en({className:e,...t}){return g.jsx(kt,{"data-slot":"sheet-overlay",className:E("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function tn({className:e,children:t,side:n="right",hideCloseButton:r=!1,...s}){return g.jsxs(Zt,{children:[g.jsx(en,{}),g.jsxs(Dt,{"data-slot":"sheet-content",className:E("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full border-l",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full border-r",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...s,children:[t,!r&&g.jsxs(Mt,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[g.jsx(jt,{className:"size-4"}),g.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function nn({className:e,...t}){return g.jsx("div",{"data-slot":"sheet-header",className:E("flex flex-col gap-1.5 p-4",e),...t})}function rn({className:e,...t}){return g.jsx(Et,{"data-slot":"sheet-title",className:E("text-foreground font-semibold",e),...t})}function sn({className:e,...t}){return g.jsx(Pt,{"data-slot":"sheet-description",className:E("text-muted-foreground text-sm",e),...t})}const on="sidebar_state",an=60*60*24*7,cn="16rem",ln="18rem",un="3rem",dn="b",st=D.createContext(null);function Ce(){const e=D.useContext(st);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Hr({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:s,children:a,...o}){const c=Ut(),[l,i]=D.useState(!1),[w,b]=D.useState(e),u=t??w,m=D.useCallback(h=>{const d=typeof h=="function"?h(u):h;n?n(d):b(d),document.cookie=`${on}=${d}; path=/; max-age=${an}`},[n,u]),f=D.useCallback(()=>c?i(h=>!h):m(h=>!h),[c,m,i]);D.useEffect(()=>{const h=d=>{d.key===dn&&(d.metaKey||d.ctrlKey)&&(d.preventDefault(),f())};return window.addEventListener("keydown",h),()=>window.removeEventListener("keydown",h)},[f]);const p=u?"expanded":"collapsed",x=D.useMemo(()=>({state:p,open:u,setOpen:m,isMobile:c,openMobile:l,setOpenMobile:i,toggleSidebar:f}),[p,u,m,c,l,i,f]);return g.jsx(st.Provider,{value:x,children:g.jsx(wt,{delayDuration:0,children:g.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":cn,"--sidebar-width-icon":un,...s},className:E("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...o,children:a})})})}function Br({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:s,...a}){const{isMobile:o,state:c,openMobile:l,setOpenMobile:i}=Ce();return n==="none"?g.jsx("div",{"data-slot":"sidebar",className:E("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...a,children:s}):o?g.jsx(Jt,{open:l,onOpenChange:i,...a,children:g.jsxs(tn,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":ln},side:e,children:[g.jsxs(nn,{className:"sr-only",children:[g.jsx(rn,{children:"Sidebar"}),g.jsx(sn,{children:"Displays the mobile sidebar."})]}),g.jsx("div",{className:"flex h-full w-full flex-col",children:s})]})}):g.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[g.jsx("div",{"data-slot":"sidebar-gap",className:E("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),g.jsx("div",{"data-slot":"sidebar-container",className:E("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...a,children:g.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function Vr({className:e,onClick:t,...n}){const{toggleSidebar:r}=Ce();return g.jsxs(se,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:E("size-7",e),onClick:s=>{t==null||t(s),r()},...n,children:[g.jsx(Yt,{}),g.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function _r({className:e,...t}){const{toggleSidebar:n}=Ce();return g.jsx("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:E("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function Fr({className:e,...t}){return g.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:E("flex flex-col gap-2 p-2",e),...t})}function Wr({className:e,...t}){return g.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:E("flex flex-col gap-2 p-2",e),...t})}function qr({className:e,...t}){return g.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:E("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function $r({className:e,...t}){return g.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:E("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Kr({className:e,asChild:t=!1,...n}){const r=t?He:"div";return g.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:E("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function Qr({className:e,...t}){return g.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:E("flex w-full min-w-0 flex-col gap-1",e),...t})}function Gr({className:e,...t}){return g.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:E("group/menu-item relative",e),...t})}const fn=vt("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Xr({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:s,className:a,...o}){const c=e?He:"button",{isMobile:l,state:i}=Ce(),w=g.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:E(fn({variant:n,size:r}),a),...o});return s?(typeof s=="string"&&(s={children:s}),g.jsxs(St,{children:[g.jsx(Tt,{asChild:!0,children:w}),g.jsx(Nt,{side:"right",align:"center",hidden:i!=="collapsed"||l,...s})]})):w}function Yr({className:e,...t}){return g.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:E("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function Ur({className:e,...t}){return g.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:E("group/menu-sub-item relative",e),...t})}function Jr({asChild:e=!1,size:t="md",isActive:n=!1,className:r,...s}){const a=e?He:"a";return g.jsx(a,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:E("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...s})}function hn(e){return Object.prototype.toString.call(e)==="[object Object]"}function Ze(e){return hn(e)||Array.isArray(e)}function mn(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function Be(e,t){const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;const s=JSON.stringify(Object.keys(e.breakpoints||{})),a=JSON.stringify(Object.keys(t.breakpoints||{}));return s!==a?!1:n.every(o=>{const c=e[o],l=t[o];return typeof c=="function"?`${c}`==`${l}`:!Ze(c)||!Ze(l)?c===l:Be(c,l)})}function et(e){return e.concat().sort((t,n)=>t.name>n.name?1:-1).map(t=>t.options)}function gn(e,t){if(e.length!==t.length)return!1;const n=et(e),r=et(t);return n.every((s,a)=>{const o=r[a];return Be(s,o)})}function Ve(e){return typeof e=="number"}function Oe(e){return typeof e=="string"}function De(e){return typeof e=="boolean"}function tt(e){return Object.prototype.toString.call(e)==="[object Object]"}function L(e){return Math.abs(e)}function _e(e){return Math.sign(e)}function be(e,t){return L(e-t)}function pn(e,t){if(e===0||t===0||L(e)<=L(t))return 0;const n=be(L(e),L(t));return L(n/e)}function bn(e){return Math.round(e*100)/100}function xe(e){return ye(e).map(Number)}function _(e){return e[ve(e)]}function ve(e){return Math.max(0,e.length-1)}function Fe(e,t){return t===ve(e)}function nt(e,t=0){return Array.from(Array(e),(n,r)=>t+r)}function ye(e){return Object.keys(e)}function ot(e,t){return[e,t].reduce((n,r)=>(ye(r).forEach(s=>{const a=n[s],o=r[s],c=tt(a)&&tt(o);n[s]=c?ot(a,o):o}),n),{})}function Re(e,t){return typeof t.MouseEvent<"u"&&e instanceof t.MouseEvent}function xn(e,t){const n={start:r,center:s,end:a};function r(){return 0}function s(l){return a(l)/2}function a(l){return t-l}function o(l,i){return Oe(e)?n[e](l):e(t,l,i)}return{measure:o}}function we(){let e=[];function t(s,a,o,c={passive:!0}){let l;if("addEventListener"in s)s.addEventListener(a,o,c),l=()=>s.removeEventListener(a,o,c);else{const i=s;i.addListener(o),l=()=>i.removeListener(o)}return e.push(l),r}function n(){e=e.filter(s=>s())}const r={add:t,clear:n};return r}function yn(e,t,n,r){const s=we(),a=1e3/60;let o=null,c=0,l=0;function i(){s.add(e,"visibilitychange",()=>{e.hidden&&f()})}function w(){m(),s.clear()}function b(x){if(!l)return;o||(o=x,n(),n());const h=x-o;for(o=x,c+=h;c>=a;)n(),c-=a;const d=c/a;r(d),l&&(l=t.requestAnimationFrame(b))}function u(){l||(l=t.requestAnimationFrame(b))}function m(){t.cancelAnimationFrame(l),o=null,c=0,l=0}function f(){o=null,c=0}return{init:i,destroy:w,start:u,stop:m,update:n,render:r}}function wn(e,t){const n=t==="rtl",r=e==="y",s=r?"y":"x",a=r?"x":"y",o=!r&&n?-1:1,c=w(),l=b();function i(f){const{height:p,width:x}=f;return r?p:x}function w(){return r?"top":n?"right":"left"}function b(){return r?"bottom":n?"left":"right"}function u(f){return f*o}return{scroll:s,cross:a,startEdge:c,endEdge:l,measureSize:i,direction:u}}function oe(e=0,t=0){const n=L(e-t);function r(i){return i<e}function s(i){return i>t}function a(i){return r(i)||s(i)}function o(i){return a(i)?r(i)?e:t:i}function c(i){return n?i-n*Math.ceil((i-t)/n):i}return{length:n,max:t,min:e,constrain:o,reachedAny:a,reachedMax:s,reachedMin:r,removeOffset:c}}function at(e,t,n){const{constrain:r}=oe(0,e),s=e+1;let a=o(t);function o(u){return n?L((s+u)%s):r(u)}function c(){return a}function l(u){return a=o(u),b}function i(u){return w().set(c()+u)}function w(){return at(e,c(),n)}const b={get:c,set:l,add:i,clone:w};return b}function vn(e,t,n,r,s,a,o,c,l,i,w,b,u,m,f,p,x,h,d){const{cross:T,direction:y}=e,C=["INPUT","SELECT","TEXTAREA"],S={passive:!1},N=we(),M=we(),j=oe(50,225).constrain(m.measure(20)),A={mouse:300,touch:400},P={mouse:500,touch:600},H=f?43:25;let F=!1,W=0,q=0,te=!1,J=!1,Q=!1,G=!1;function ue(v){if(!d)return;function I(z){(De(d)||d(v,z))&&fe(z)}const O=t;N.add(O,"dragstart",z=>z.preventDefault(),S).add(O,"touchmove",()=>{},S).add(O,"touchend",()=>{}).add(O,"touchstart",I).add(O,"mousedown",I).add(O,"touchcancel",R).add(O,"contextmenu",R).add(O,"click",Y,!0)}function $(){N.clear(),M.clear()}function ae(){const v=G?n:t;M.add(v,"touchmove",B,S).add(v,"touchend",R).add(v,"mousemove",B,S).add(v,"mouseup",R)}function ie(v){const I=v.nodeName||"";return C.includes(I)}function X(){return(f?P:A)[G?"mouse":"touch"]}function de(v,I){const O=b.add(_e(v)*-1),z=w.byDistance(v,!f).distance;return f||L(v)<j?z:x&&I?z*.5:w.byIndex(O.get(),0).distance}function fe(v){const I=Re(v,r);G=I,Q=f&&I&&!v.buttons&&F,F=be(s.get(),o.get())>=2,!(I&&v.button!==0)&&(ie(v.target)||(te=!0,a.pointerDown(v),i.useFriction(0).useDuration(0),s.set(o),ae(),W=a.readPoint(v),q=a.readPoint(v,T),u.emit("pointerDown")))}function B(v){if(!Re(v,r)&&v.touches.length>=2)return R(v);const O=a.readPoint(v),z=a.readPoint(v,T),K=be(O,W),U=be(z,q);if(!J&&!G&&(!v.cancelable||(J=K>U,!J)))return R(v);const ne=a.pointerMove(v);K>p&&(Q=!0),i.useFriction(.3).useDuration(.75),c.start(),s.add(y(ne)),v.preventDefault()}function R(v){const O=w.byDistance(0,!1).index!==b.get(),z=a.pointerUp(v)*X(),K=de(y(z),O),U=pn(z,K),ne=H-10*U,Z=h+U/50;J=!1,te=!1,M.clear(),i.useDuration(ne).useFriction(Z),l.distance(K,!f),G=!1,u.emit("pointerUp")}function Y(v){Q&&(v.stopPropagation(),v.preventDefault(),Q=!1)}function V(){return te}return{init:ue,destroy:$,pointerDown:V}}function Sn(e,t){let r,s;function a(b){return b.timeStamp}function o(b,u){const f=`client${(u||e.scroll)==="x"?"X":"Y"}`;return(Re(b,t)?b:b.touches[0])[f]}function c(b){return r=b,s=b,o(b)}function l(b){const u=o(b)-o(s),m=a(b)-a(r)>170;return s=b,m&&(r=b),u}function i(b){if(!r||!s)return 0;const u=o(s)-o(r),m=a(b)-a(r),f=a(b)-a(s)>170,p=u/m;return m&&!f&&L(p)>.1?p:0}return{pointerDown:c,pointerMove:l,pointerUp:i,readPoint:o}}function Tn(){function e(n){const{offsetTop:r,offsetLeft:s,offsetWidth:a,offsetHeight:o}=n;return{top:r,right:s+a,bottom:r+o,left:s,width:a,height:o}}return{measure:e}}function Nn(e){function t(r){return e*(r/100)}return{measure:t}}function Cn(e,t,n,r,s,a,o){const c=[e].concat(r);let l,i,w=[],b=!1;function u(x){return s.measureSize(o.measure(x))}function m(x){if(!a)return;i=u(e),w=r.map(u);function h(d){for(const T of d){if(b)return;const y=T.target===e,C=r.indexOf(T.target),S=y?i:w[C],N=u(y?e:r[C]);if(L(N-S)>=.5){x.reInit(),t.emit("resize");break}}}l=new ResizeObserver(d=>{(De(a)||a(x,d))&&h(d)}),n.requestAnimationFrame(()=>{c.forEach(d=>l.observe(d))})}function f(){b=!0,l&&l.disconnect()}return{init:m,destroy:f}}function Dn(e,t,n,r,s,a){let o=0,c=0,l=s,i=a,w=e.get(),b=0;function u(){const S=r.get()-e.get(),N=!l;let M=0;return N?(o=0,n.set(r),e.set(r),M=S):(n.set(e),o+=S/l,o*=i,w+=o,e.add(o),M=w-b),c=_e(M),b=w,C}function m(){const S=r.get()-t.get();return L(S)<.001}function f(){return l}function p(){return c}function x(){return o}function h(){return T(s)}function d(){return y(a)}function T(S){return l=S,C}function y(S){return i=S,C}const C={direction:p,duration:f,velocity:x,seek:u,settled:m,useBaseFriction:d,useBaseDuration:h,useFriction:y,useDuration:T};return C}function Mn(e,t,n,r,s){const a=s.measure(10),o=s.measure(50),c=oe(.1,.99);let l=!1;function i(){return!(l||!e.reachedAny(n.get())||!e.reachedAny(t.get()))}function w(m){if(!i())return;const f=e.reachedMin(t.get())?"min":"max",p=L(e[f]-t.get()),x=n.get()-t.get(),h=c.constrain(p/o);n.subtract(x*h),!m&&L(x)<a&&(n.set(e.constrain(n.get())),r.useDuration(25).useBaseFriction())}function b(m){l=!m}return{shouldConstrain:i,constrain:w,toggleActive:b}}function jn(e,t,n,r,s){const a=oe(-t+e,0),o=b(),c=w(),l=u();function i(f,p){return be(f,p)<=1}function w(){const f=o[0],p=_(o),x=o.lastIndexOf(f),h=o.indexOf(p)+1;return oe(x,h)}function b(){return n.map((f,p)=>{const{min:x,max:h}=a,d=a.constrain(f),T=!p,y=Fe(n,p);return T?h:y||i(x,d)?x:i(h,d)?h:d}).map(f=>parseFloat(f.toFixed(3)))}function u(){if(t<=e+s)return[a.max];if(r==="keepSnaps")return o;const{min:f,max:p}=c;return o.slice(f,p)}return{snapsContained:l,scrollContainLimit:c}}function En(e,t,n){const r=t[0],s=n?r-e:_(t);return{limit:oe(s,r)}}function Pn(e,t,n,r){const a=t.min+.1,o=t.max+.1,{reachedMin:c,reachedMax:l}=oe(a,o);function i(u){return u===1?l(n.get()):u===-1?c(n.get()):!1}function w(u){if(!i(u))return;const m=e*(u*-1);r.forEach(f=>f.add(m))}return{loop:w}}function In(e){const{max:t,length:n}=e;function r(a){const o=a-t;return n?o/-n:0}return{get:r}}function kn(e,t,n,r,s){const{startEdge:a,endEdge:o}=e,{groupSlides:c}=s,l=b().map(t.measure),i=u(),w=m();function b(){return c(r).map(p=>_(p)[o]-p[0][a]).map(L)}function u(){return r.map(p=>n[a]-p[a]).map(p=>-L(p))}function m(){return c(i).map(p=>p[0]).map((p,x)=>p+l[x])}return{snaps:i,snapsAligned:w}}function Ln(e,t,n,r,s,a){const{groupSlides:o}=s,{min:c,max:l}=r,i=w();function w(){const u=o(a),m=!e||t==="keepSnaps";return n.length===1?[a]:m?u:u.slice(c,l).map((f,p,x)=>{const h=!p,d=Fe(x,p);if(h){const T=_(x[0])+1;return nt(T)}if(d){const T=ve(a)-_(x)[0]+1;return nt(T,_(x)[0])}return f})}return{slideRegistry:i}}function An(e,t,n,r,s){const{reachedAny:a,removeOffset:o,constrain:c}=r;function l(f){return f.concat().sort((p,x)=>L(p)-L(x))[0]}function i(f){const p=e?o(f):c(f),x=t.map((d,T)=>({diff:w(d-p,0),index:T})).sort((d,T)=>L(d.diff)-L(T.diff)),{index:h}=x[0];return{index:h,distance:p}}function w(f,p){const x=[f,f+n,f-n];if(!e)return f;if(!p)return l(x);const h=x.filter(d=>_e(d)===p);return h.length?l(h):_(x)-n}function b(f,p){const x=t[f]-s.get(),h=w(x,p);return{index:f,distance:h}}function u(f,p){const x=s.get()+f,{index:h,distance:d}=i(x),T=!e&&a(x);if(!p||T)return{index:h,distance:f};const y=t[h]-d,C=f+w(y,0);return{index:h,distance:C}}return{byDistance:u,byIndex:b,shortcut:w}}function On(e,t,n,r,s,a,o){function c(b){const u=b.distance,m=b.index!==t.get();a.add(u),u&&(r.duration()?e.start():(e.update(),e.render(1),e.update())),m&&(n.set(t.get()),t.set(b.index),o.emit("select"))}function l(b,u){const m=s.byDistance(b,u);c(m)}function i(b,u){const m=t.clone().set(b),f=s.byIndex(m.get(),u);c(f)}return{distance:l,index:i}}function Rn(e,t,n,r,s,a,o,c){const l={passive:!0,capture:!0};let i=0;function w(m){if(!c)return;function f(p){if(new Date().getTime()-i>10)return;o.emit("slideFocusStart"),e.scrollLeft=0;const d=n.findIndex(T=>T.includes(p));Ve(d)&&(s.useDuration(0),r.index(d,0),o.emit("slideFocus"))}a.add(document,"keydown",b,!1),t.forEach((p,x)=>{a.add(p,"focus",h=>{(De(c)||c(m,h))&&f(x)},l)})}function b(m){m.code==="Tab"&&(i=new Date().getTime())}return{init:w}}function pe(e){let t=e;function n(){return t}function r(l){t=o(l)}function s(l){t+=o(l)}function a(l){t-=o(l)}function o(l){return Ve(l)?l:l.get()}return{get:n,set:r,add:s,subtract:a}}function it(e,t){const n=e.scroll==="x"?o:c,r=t.style;let s=null,a=!1;function o(u){return`translate3d(${u}px,0px,0px)`}function c(u){return`translate3d(0px,${u}px,0px)`}function l(u){if(a)return;const m=bn(e.direction(u));m!==s&&(r.transform=n(m),s=m)}function i(u){a=!u}function w(){a||(r.transform="",t.getAttribute("style")||t.removeAttribute("style"))}return{clear:w,to:l,toggleActive:i}}function zn(e,t,n,r,s,a,o,c,l){const w=xe(s),b=xe(s).reverse(),u=h().concat(d());function m(N,M){return N.reduce((j,A)=>j-s[A],M)}function f(N,M){return N.reduce((j,A)=>m(j,M)>0?j.concat([A]):j,[])}function p(N){return a.map((M,j)=>({start:M-r[j]+.5+N,end:M+t-.5+N}))}function x(N,M,j){const A=p(M);return N.map(P=>{const H=j?0:-n,F=j?n:0,W=j?"end":"start",q=A[P][W];return{index:P,loopPoint:q,slideLocation:pe(-1),translate:it(e,l[P]),target:()=>c.get()>q?H:F}})}function h(){const N=o[0],M=f(b,N);return x(M,n,!1)}function d(){const N=t-o[0]-1,M=f(w,N);return x(M,-n,!0)}function T(){return u.every(({index:N})=>{const M=w.filter(j=>j!==N);return m(M,t)<=.1})}function y(){u.forEach(N=>{const{target:M,translate:j,slideLocation:A}=N,P=M();P!==A.get()&&(j.to(P),A.set(P))})}function C(){u.forEach(N=>N.translate.clear())}return{canLoop:T,clear:C,loop:y,loopPoints:u}}function Hn(e,t,n){let r,s=!1;function a(l){if(!n)return;function i(w){for(const b of w)if(b.type==="childList"){l.reInit(),t.emit("slidesChanged");break}}r=new MutationObserver(w=>{s||(De(n)||n(l,w))&&i(w)}),r.observe(e,{childList:!0})}function o(){r&&r.disconnect(),s=!0}return{init:a,destroy:o}}function Bn(e,t,n,r){const s={};let a=null,o=null,c,l=!1;function i(){c=new IntersectionObserver(f=>{l||(f.forEach(p=>{const x=t.indexOf(p.target);s[x]=p}),a=null,o=null,n.emit("slidesInView"))},{root:e.parentElement,threshold:r}),t.forEach(f=>c.observe(f))}function w(){c&&c.disconnect(),l=!0}function b(f){return ye(s).reduce((p,x)=>{const h=parseInt(x),{isIntersecting:d}=s[h];return(f&&d||!f&&!d)&&p.push(h),p},[])}function u(f=!0){if(f&&a)return a;if(!f&&o)return o;const p=b(f);return f&&(a=p),f||(o=p),p}return{init:i,destroy:w,get:u}}function Vn(e,t,n,r,s,a){const{measureSize:o,startEdge:c,endEdge:l}=e,i=n[0]&&s,w=f(),b=p(),u=n.map(o),m=x();function f(){if(!i)return 0;const d=n[0];return L(t[c]-d[c])}function p(){if(!i)return 0;const d=a.getComputedStyle(_(r));return parseFloat(d.getPropertyValue(`margin-${l}`))}function x(){return n.map((d,T,y)=>{const C=!T,S=Fe(y,T);return C?u[T]+w:S?u[T]+b:y[T+1][c]-d[c]}).map(L)}return{slideSizes:u,slideSizesWithGaps:m,startGap:w,endGap:b}}function _n(e,t,n,r,s,a,o,c,l){const{startEdge:i,endEdge:w,direction:b}=e,u=Ve(n);function m(h,d){return xe(h).filter(T=>T%d===0).map(T=>h.slice(T,T+d))}function f(h){return h.length?xe(h).reduce((d,T,y)=>{const C=_(d)||0,S=C===0,N=T===ve(h),M=s[i]-a[C][i],j=s[i]-a[T][w],A=!r&&S?b(o):0,P=!r&&N?b(c):0,H=L(j-P-(M+A));return y&&H>t+l&&d.push(T),N&&d.push(h.length),d},[]).map((d,T,y)=>{const C=Math.max(y[T-1]||0);return h.slice(C,d)}):[]}function p(h){return u?m(h,n):f(h)}return{groupSlides:p}}function Fn(e,t,n,r,s,a,o){const{align:c,axis:l,direction:i,startIndex:w,loop:b,duration:u,dragFree:m,dragThreshold:f,inViewThreshold:p,slidesToScroll:x,skipSnaps:h,containScroll:d,watchResize:T,watchSlides:y,watchDrag:C,watchFocus:S}=a,N=2,M=Tn(),j=M.measure(t),A=n.map(M.measure),P=wn(l,i),H=P.measureSize(j),F=Nn(H),W=xn(c,H),q=!b&&!!d,te=b||!!d,{slideSizes:J,slideSizesWithGaps:Q,startGap:G,endGap:ue}=Vn(P,j,A,n,te,s),$=_n(P,H,x,b,j,A,G,ue,N),{snaps:ae,snapsAligned:ie}=kn(P,W,j,A,$),X=-_(ae)+_(Q),{snapsContained:de,scrollContainLimit:fe}=jn(H,X,ie,d,N),B=q?de:ie,{limit:R}=En(X,B,b),Y=at(ve(B),w,b),V=Y.clone(),k=xe(n),v=({dragHandler:ce,scrollBody:Ie,scrollBounds:ke,options:{loop:Se}})=>{Se||ke.constrain(ce.pointerDown()),Ie.seek()},I=({scrollBody:ce,translate:Ie,location:ke,offsetLocation:Se,previousLocation:ft,scrollLooper:ht,slideLooper:mt,dragHandler:gt,animation:pt,eventHandler:Qe,scrollBounds:bt,options:{loop:Ge}},Xe)=>{const Ye=ce.settled(),xt=!bt.shouldConstrain(),Ue=Ge?Ye:Ye&&xt,Je=Ue&&!gt.pointerDown();Je&&pt.stop();const yt=ke.get()*Xe+ft.get()*(1-Xe);Se.set(yt),Ge&&(ht.loop(ce.direction()),mt.loop()),Ie.to(Se.get()),Je&&Qe.emit("settle"),Ue||Qe.emit("scroll")},O=yn(r,s,()=>v(Pe),ce=>I(Pe,ce)),z=.68,K=B[Y.get()],U=pe(K),ne=pe(K),Z=pe(K),re=pe(K),he=Dn(U,Z,ne,re,u,z),je=An(b,B,X,R,re),Ee=On(O,Y,V,he,je,re,o),qe=In(R),$e=we(),ut=Bn(t,n,o,p),{slideRegistry:Ke}=Ln(q,d,B,fe,$,k),dt=Rn(e,n,Ke,Ee,he,$e,o,S),Pe={ownerDocument:r,ownerWindow:s,eventHandler:o,containerRect:j,slideRects:A,animation:O,axis:P,dragHandler:vn(P,e,r,s,re,Sn(P,s),U,O,Ee,he,je,Y,o,F,m,f,h,z,C),eventStore:$e,percentOfView:F,index:Y,indexPrevious:V,limit:R,location:U,offsetLocation:Z,previousLocation:ne,options:a,resizeHandler:Cn(t,o,s,n,P,T,M),scrollBody:he,scrollBounds:Mn(R,Z,re,he,F),scrollLooper:Pn(X,R,Z,[U,Z,ne,re]),scrollProgress:qe,scrollSnapList:B.map(qe.get),scrollSnaps:B,scrollTarget:je,scrollTo:Ee,slideLooper:zn(P,H,X,J,Q,ae,B,Z,n),slideFocus:dt,slidesHandler:Hn(t,o,y),slidesInView:ut,slideIndexes:k,slideRegistry:Ke,slidesToScroll:$,target:re,translate:it(P,t)};return Pe}function Wn(){let e={},t;function n(i){t=i}function r(i){return e[i]||[]}function s(i){return r(i).forEach(w=>w(t,i)),l}function a(i,w){return e[i]=r(i).concat([w]),l}function o(i,w){return e[i]=r(i).filter(b=>b!==w),l}function c(){e={}}const l={init:n,emit:s,off:o,on:a,clear:c};return l}const qn={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function $n(e){function t(a,o){return ot(a,o||{})}function n(a){const o=a.breakpoints||{},c=ye(o).filter(l=>e.matchMedia(l).matches).map(l=>o[l]).reduce((l,i)=>t(l,i),{});return t(a,c)}function r(a){return a.map(o=>ye(o.breakpoints||{})).reduce((o,c)=>o.concat(c),[]).map(e.matchMedia)}return{mergeOptions:t,optionsAtMedia:n,optionsMediaQueries:r}}function Kn(e){let t=[];function n(a,o){return t=o.filter(({options:c})=>e.optionsAtMedia(c).active!==!1),t.forEach(c=>c.init(a,e)),o.reduce((c,l)=>Object.assign(c,{[l.name]:l}),{})}function r(){t=t.filter(a=>a.destroy())}return{init:n,destroy:r}}function Ne(e,t,n){const r=e.ownerDocument,s=r.defaultView,a=$n(s),o=Kn(a),c=we(),l=Wn(),{mergeOptions:i,optionsAtMedia:w,optionsMediaQueries:b}=a,{on:u,off:m,emit:f}=l,p=P;let x=!1,h,d=i(qn,Ne.globalOptions),T=i(d),y=[],C,S,N;function M(){const{container:k,slides:v}=T;S=(Oe(k)?e.querySelector(k):k)||e.children[0];const O=Oe(v)?S.querySelectorAll(v):v;N=[].slice.call(O||S.children)}function j(k){const v=Fn(e,S,N,r,s,k,l);if(k.loop&&!v.slideLooper.canLoop()){const I=Object.assign({},k,{loop:!1});return j(I)}return v}function A(k,v){x||(d=i(d,k),T=w(d),y=v||y,M(),h=j(T),b([d,...y.map(({options:I})=>I)]).forEach(I=>c.add(I,"change",P)),T.active&&(h.translate.to(h.location.get()),h.animation.init(),h.slidesInView.init(),h.slideFocus.init(V),h.eventHandler.init(V),h.resizeHandler.init(V),h.slidesHandler.init(V),h.options.loop&&h.slideLooper.loop(),S.offsetParent&&N.length&&h.dragHandler.init(V),C=o.init(V,y)))}function P(k,v){const I=$();H(),A(i({startIndex:I},k),v),l.emit("reInit")}function H(){h.dragHandler.destroy(),h.eventStore.clear(),h.translate.clear(),h.slideLooper.clear(),h.resizeHandler.destroy(),h.slidesHandler.destroy(),h.slidesInView.destroy(),h.animation.destroy(),o.destroy(),c.clear()}function F(){x||(x=!0,c.clear(),H(),l.emit("destroy"),l.clear())}function W(k,v,I){!T.active||x||(h.scrollBody.useBaseFriction().useDuration(v===!0?0:T.duration),h.scrollTo.index(k,I||0))}function q(k){const v=h.index.add(1).get();W(v,k,-1)}function te(k){const v=h.index.add(-1).get();W(v,k,1)}function J(){return h.index.add(1).get()!==$()}function Q(){return h.index.add(-1).get()!==$()}function G(){return h.scrollSnapList}function ue(){return h.scrollProgress.get(h.offsetLocation.get())}function $(){return h.index.get()}function ae(){return h.indexPrevious.get()}function ie(){return h.slidesInView.get()}function X(){return h.slidesInView.get(!1)}function de(){return C}function fe(){return h}function B(){return e}function R(){return S}function Y(){return N}const V={canScrollNext:J,canScrollPrev:Q,containerNode:R,internalEngine:fe,destroy:F,off:m,on:u,emit:f,plugins:de,previousScrollSnap:ae,reInit:p,rootNode:B,scrollNext:q,scrollPrev:te,scrollProgress:ue,scrollSnapList:G,scrollTo:W,selectedScrollSnap:$,slideNodes:Y,slidesInView:ie,slidesNotInView:X};return A(t,n),setTimeout(()=>l.emit("init"),0),V}Ne.globalOptions=void 0;function We(e={},t=[]){const n=D.useRef(e),r=D.useRef(t),[s,a]=D.useState(),[o,c]=D.useState(),l=D.useCallback(()=>{s&&s.reInit(n.current,r.current)},[s]);return D.useEffect(()=>{Be(n.current,e)||(n.current=e,l())},[e,l]),D.useEffect(()=>{gn(r.current,t)||(r.current=t,l())},[t,l]),D.useEffect(()=>{if(mn()&&o){Ne.globalOptions=We.globalOptions;const i=Ne(o,n.current,r.current);return a(i),()=>i.destroy()}else a(void 0)},[o,a]),[c,s]}We.globalOptions=void 0;const ct=ee.createContext(null);function Me(){const e=ee.useContext(ct);if(!e)throw new Error("useCarousel must be used within a <Carousel />");return e}const Qn=ee.forwardRef(({orientation:e="horizontal",opts:t,setApi:n,plugins:r,className:s,children:a,...o},c)=>{const[l,i]=We({...t,axis:e==="horizontal"?"x":"y"},r),[w,b]=D.useState(!1),[u,m]=D.useState(!1),f=D.useCallback(d=>{d&&(b(d.canScrollPrev()),m(d.canScrollNext()))},[]),p=D.useCallback(()=>{i==null||i.scrollPrev()},[i]),x=D.useCallback(()=>{i==null||i.scrollNext()},[i]),h=D.useCallback(d=>{d.key==="ArrowLeft"?(d.preventDefault(),p()):d.key==="ArrowRight"&&(d.preventDefault(),x())},[p,x]);return D.useEffect(()=>{!i||!n||n(i)},[i,n]),D.useEffect(()=>{if(i)return f(i),i.on("reInit",f),i.on("select",f),()=>{i==null||i.off("select",f)}},[i,f]),g.jsx(ct.Provider,{value:{carouselRef:l,api:i,opts:t,orientation:e||((t==null?void 0:t.axis)==="y"?"vertical":"horizontal"),scrollPrev:p,scrollNext:x,canScrollPrev:w,canScrollNext:u},children:g.jsx("div",{ref:c,onKeyDownCapture:h,className:E("relative",s),role:"region","aria-roledescription":"carousel",...o,children:a})})});Qn.displayName="Carousel";const Gn=ee.forwardRef(({className:e,...t},n)=>{const{carouselRef:r,orientation:s}=Me();return g.jsx("div",{ref:r,className:"overflow-hidden",children:g.jsx("div",{ref:n,className:E("flex",s==="horizontal"?"-ml-4":"-mt-4 flex-col",e),...t})})});Gn.displayName="CarouselContent";const Xn=ee.forwardRef(({className:e,...t},n)=>{const{orientation:r}=Me();return g.jsx("div",{ref:n,role:"group","aria-roledescription":"slide",className:E("min-w-0 shrink-0 grow-0 basis-full",r==="horizontal"?"pl-4":"pt-4",e),...t})});Xn.displayName="CarouselItem";const Yn=ee.forwardRef(({className:e,variant:t="outline",size:n="icon",...r},s)=>{const{orientation:a,scrollPrev:o,canScrollPrev:c}=Me();return g.jsxs(se,{ref:s,variant:t,size:n,className:E("absolute h-8 w-8 rounded-full",a==="horizontal"?"top-1/2 -left-12 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:o,...r,children:[g.jsx(Ot,{className:"h-4 w-4"}),g.jsx("span",{className:"sr-only",children:"Previous slide"})]})});Yn.displayName="CarouselPrevious";const Un=ee.forwardRef(({className:e,variant:t="outline",size:n="icon",...r},s)=>{const{orientation:a,scrollNext:o,canScrollNext:c}=Me();return g.jsxs(se,{ref:s,variant:t,size:n,className:E("absolute h-8 w-8 rounded-full",a==="horizontal"?"top-1/2 -right-12 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:o,...r,children:[g.jsx(Rt,{className:"h-4 w-4"}),g.jsx("span",{className:"sr-only",children:"Next slide"})]})});Un.displayName="CarouselNext";const Jn={lessThanXSeconds:{one:"dưới 1 giây",other:"dưới {{count}} giây"},xSeconds:{one:"1 giây",other:"{{count}} giây"},halfAMinute:"nửa phút",lessThanXMinutes:{one:"dưới 1 phút",other:"dưới {{count}} phút"},xMinutes:{one:"1 phút",other:"{{count}} phút"},aboutXHours:{one:"khoảng 1 giờ",other:"khoảng {{count}} giờ"},xHours:{one:"1 giờ",other:"{{count}} giờ"},xDays:{one:"1 ngày",other:"{{count}} ngày"},aboutXWeeks:{one:"khoảng 1 tuần",other:"khoảng {{count}} tuần"},xWeeks:{one:"1 tuần",other:"{{count}} tuần"},aboutXMonths:{one:"khoảng 1 tháng",other:"khoảng {{count}} tháng"},xMonths:{one:"1 tháng",other:"{{count}} tháng"},aboutXYears:{one:"khoảng 1 năm",other:"khoảng {{count}} năm"},xYears:{one:"1 năm",other:"{{count}} năm"},overXYears:{one:"hơn 1 năm",other:"hơn {{count}} năm"},almostXYears:{one:"gần 1 năm",other:"gần {{count}} năm"}},Zn=(e,t,n)=>{let r;const s=Jn[e];return typeof s=="string"?r=s:t===1?r=s.one:r=s.other.replace("{{count}}",String(t)),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?r+" nữa":r+" trước":r},er={full:"EEEE, 'ngày' d MMMM 'năm' y",long:"'ngày' d MMMM 'năm' y",medium:"d MMM 'năm' y",short:"dd/MM/y"},tr={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},nr={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},rr={date:Le({formats:er,defaultWidth:"full"}),time:Le({formats:tr,defaultWidth:"full"}),dateTime:Le({formats:nr,defaultWidth:"full"})},sr={lastWeek:"eeee 'tuần trước vào lúc' p",yesterday:"'hôm qua vào lúc' p",today:"'hôm nay vào lúc' p",tomorrow:"'ngày mai vào lúc' p",nextWeek:"eeee 'tới vào lúc' p",other:"P"},or=(e,t,n,r)=>sr[e],ar={narrow:["TCN","SCN"],abbreviated:["trước CN","sau CN"],wide:["trước Công Nguyên","sau Công Nguyên"]},ir={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["Quý 1","Quý 2","Quý 3","Quý 4"]},cr={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["quý I","quý II","quý III","quý IV"]},lr={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["Thg 1","Thg 2","Thg 3","Thg 4","Thg 5","Thg 6","Thg 7","Thg 8","Thg 9","Thg 10","Thg 11","Thg 12"],wide:["Tháng Một","Tháng Hai","Tháng Ba","Tháng Tư","Tháng Năm","Tháng Sáu","Tháng Bảy","Tháng Tám","Tháng Chín","Tháng Mười","Tháng Mười Một","Tháng Mười Hai"]},ur={narrow:["01","02","03","04","05","06","07","08","09","10","11","12"],abbreviated:["thg 1","thg 2","thg 3","thg 4","thg 5","thg 6","thg 7","thg 8","thg 9","thg 10","thg 11","thg 12"],wide:["tháng 01","tháng 02","tháng 03","tháng 04","tháng 05","tháng 06","tháng 07","tháng 08","tháng 09","tháng 10","tháng 11","tháng 12"]},dr={narrow:["CN","T2","T3","T4","T5","T6","T7"],short:["CN","Th 2","Th 3","Th 4","Th 5","Th 6","Th 7"],abbreviated:["CN","Thứ 2","Thứ 3","Thứ 4","Thứ 5","Thứ 6","Thứ 7"],wide:["Chủ Nhật","Thứ Hai","Thứ Ba","Thứ Tư","Thứ Năm","Thứ Sáu","Thứ Bảy"]},fr={narrow:{am:"am",pm:"pm",midnight:"nửa đêm",noon:"tr",morning:"sg",afternoon:"ch",evening:"tối",night:"đêm"},abbreviated:{am:"AM",pm:"PM",midnight:"nửa đêm",noon:"trưa",morning:"sáng",afternoon:"chiều",evening:"tối",night:"đêm"},wide:{am:"SA",pm:"CH",midnight:"nửa đêm",noon:"trưa",morning:"sáng",afternoon:"chiều",evening:"tối",night:"đêm"}},hr={narrow:{am:"am",pm:"pm",midnight:"nửa đêm",noon:"tr",morning:"sg",afternoon:"ch",evening:"tối",night:"đêm"},abbreviated:{am:"AM",pm:"PM",midnight:"nửa đêm",noon:"trưa",morning:"sáng",afternoon:"chiều",evening:"tối",night:"đêm"},wide:{am:"SA",pm:"CH",midnight:"nửa đêm",noon:"giữa trưa",morning:"vào buổi sáng",afternoon:"vào buổi chiều",evening:"vào buổi tối",night:"vào ban đêm"}},mr=(e,t)=>{const n=Number(e),r=t==null?void 0:t.unit;if(r==="quarter")switch(n){case 1:return"I";case 2:return"II";case 3:return"III";case 4:return"IV"}else if(r==="day")switch(n){case 1:return"thứ 2";case 2:return"thứ 3";case 3:return"thứ 4";case 4:return"thứ 5";case 5:return"thứ 6";case 6:return"thứ 7";case 7:return"chủ nhật"}else{if(r==="week")return n===1?"thứ nhất":"thứ "+n;if(r==="dayOfYear")return n===1?"đầu tiên":"thứ "+n}return String(n)},gr={ordinalNumber:mr,era:me({values:ar,defaultWidth:"wide"}),quarter:me({values:ir,defaultWidth:"wide",formattingValues:cr,defaultFormattingWidth:"wide",argumentCallback:e=>e-1}),month:me({values:lr,defaultWidth:"wide",formattingValues:ur,defaultFormattingWidth:"wide"}),day:me({values:dr,defaultWidth:"wide"}),dayPeriod:me({values:fr,defaultWidth:"wide",formattingValues:hr,defaultFormattingWidth:"wide"})},pr=/^(\d+)/i,br=/\d+/i,xr={narrow:/^(tcn|scn)/i,abbreviated:/^(trước CN|sau CN)/i,wide:/^(trước Công Nguyên|sau Công Nguyên)/i},yr={any:[/^t/i,/^s/i]},wr={narrow:/^([1234]|i{1,3}v?)/i,abbreviated:/^q([1234]|i{1,3}v?)/i,wide:/^quý ([1234]|i{1,3}v?)/i},vr={any:[/(1|i)$/i,/(2|ii)$/i,/(3|iii)$/i,/(4|iv)$/i]},Sr={narrow:/^(0?[2-9]|10|11|12|0?1)/i,abbreviated:/^thg[ _]?(0?[1-9](?!\d)|10|11|12)/i,wide:/^tháng ?(Một|Hai|Ba|Tư|Năm|Sáu|Bảy|Tám|Chín|Mười|Mười ?Một|Mười ?Hai|0?[1-9](?!\d)|10|11|12)/i},Tr={narrow:[/0?1$/i,/0?2/i,/3/,/4/,/5/,/6/,/7/,/8/,/9/,/10/,/11/,/12/],abbreviated:[/^thg[ _]?0?1(?!\d)/i,/^thg[ _]?0?2/i,/^thg[ _]?0?3/i,/^thg[ _]?0?4/i,/^thg[ _]?0?5/i,/^thg[ _]?0?6/i,/^thg[ _]?0?7/i,/^thg[ _]?0?8/i,/^thg[ _]?0?9/i,/^thg[ _]?10/i,/^thg[ _]?11/i,/^thg[ _]?12/i],wide:[/^tháng ?(Một|0?1(?!\d))/i,/^tháng ?(Hai|0?2)/i,/^tháng ?(Ba|0?3)/i,/^tháng ?(Tư|0?4)/i,/^tháng ?(Năm|0?5)/i,/^tháng ?(Sáu|0?6)/i,/^tháng ?(Bảy|0?7)/i,/^tháng ?(Tám|0?8)/i,/^tháng ?(Chín|0?9)/i,/^tháng ?(Mười|10)/i,/^tháng ?(Mười ?Một|11)/i,/^tháng ?(Mười ?Hai|12)/i]},Nr={narrow:/^(CN|T2|T3|T4|T5|T6|T7)/i,short:/^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,abbreviated:/^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,wide:/^(Chủ ?Nhật|Chúa ?Nhật|thứ ?Hai|thứ ?Ba|thứ ?Tư|thứ ?Năm|thứ ?Sáu|thứ ?Bảy)/i},Cr={narrow:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],short:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],abbreviated:[/CN/i,/2/i,/3/i,/4/i,/5/i,/6/i,/7/i],wide:[/(Chủ|Chúa) ?Nhật/i,/Hai/i,/Ba/i,/Tư/i,/Năm/i,/Sáu/i,/Bảy/i]},Dr={narrow:/^(a|p|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,abbreviated:/^(am|pm|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,wide:/^(ch[^i]*|sa|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i},Mr={any:{am:/^(a|sa)/i,pm:/^(p|ch[^i]*)/i,midnight:/nửa đêm/i,noon:/trưa/i,morning:/sáng/i,afternoon:/chiều/i,evening:/tối/i,night:/^đêm/i}},jr={ordinalNumber:Gt({matchPattern:pr,parsePattern:br,valueCallback:e=>parseInt(e,10)}),era:ge({matchPatterns:xr,defaultMatchWidth:"wide",parsePatterns:yr,defaultParseWidth:"any"}),quarter:ge({matchPatterns:wr,defaultMatchWidth:"wide",parsePatterns:vr,defaultParseWidth:"any",valueCallback:e=>e+1}),month:ge({matchPatterns:Sr,defaultMatchWidth:"wide",parsePatterns:Tr,defaultParseWidth:"wide"}),day:ge({matchPatterns:Nr,defaultMatchWidth:"wide",parsePatterns:Cr,defaultParseWidth:"wide"}),dayPeriod:ge({matchPatterns:Dr,defaultMatchWidth:"wide",parsePatterns:Mr,defaultParseWidth:"any"})},Er={code:"vi",formatDistance:Zn,formatLong:rr,formatRelative:or,localize:gr,match:jr,options:{weekStartsOn:1,firstWeekContainsDate:1}},ze=({value:e,onChange:t})=>{const[n,r]=ee.useState(()=>{const u=e?new Date(e):new Date;return{day:u.getDate(),month:u.getMonth()+1,year:u.getFullYear()}}),s=D.useRef(null),a=D.useRef(null),o=D.useRef(null);D.useEffect(()=>{const u=e?new Date(e):new Date;r({day:u.getDate(),month:u.getMonth()+1,year:u.getFullYear()})},[e]);const c=(u,m)=>{if(u==="day"&&(m<1||m>31)||u==="month"&&(m<1||m>12)||u==="year"&&(m<1e3||m>9999))return!1;const f={...n,[u]:m},p=new Date(f.year,f.month-1,f.day);return p.getFullYear()===f.year&&p.getMonth()+1===f.month&&p.getDate()===f.day},l=u=>m=>{const f=m.target.value?Number(m.target.value):"",p=typeof f=="number"&&c(u,f),x={...n,[u]:f};r(x),p&&t(new Date(x.year,x.month-1,x.day))},i=D.useRef(n),w=u=>m=>{if(!m.target.value){r(i.current);return}const f=Number(m.target.value);c(u,f)?i.current={...n,[u]:f}:r(i.current)},b=u=>m=>{var f,p,x,h;if(!(m.metaKey||m.ctrlKey)){if(!/^[0-9]$/.test(m.key)&&!["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Delete","Tab","Backspace","Enter"].includes(m.key)){m.preventDefault();return}if(m.key==="ArrowUp"){m.preventDefault();let d={...n};u==="day"&&(n[u]===new Date(n.year,n.month,0).getDate()?(d={...d,day:1,month:n.month%12+1},d.month===1&&(d.year+=1)):d.day+=1),u==="month"&&(n[u]===12?d={...d,month:1,year:n.year+1}:d.month+=1),u==="year"&&(d.year+=1),r(d),t(new Date(d.year,d.month-1,d.day))}else if(m.key==="ArrowDown"){m.preventDefault();let d={...n};u==="day"&&(n[u]===1?(d.month-=1,d.month===0&&(d.month=12,d.year-=1),d.day=new Date(d.year,d.month,0).getDate()):d.day-=1),u==="month"&&(n[u]===1?d={...d,month:12,year:n.year-1}:d.month-=1),u==="year"&&(d.year-=1),r(d),t(new Date(d.year,d.month-1,d.day))}m.key==="ArrowRight"?(m.currentTarget.selectionStart===m.currentTarget.value.length||m.currentTarget.selectionStart===0&&m.currentTarget.selectionEnd===m.currentTarget.value.length)&&(m.preventDefault(),u==="day"&&((f=s.current)==null||f.focus()),u==="month"&&((p=o.current)==null||p.focus())):m.key==="ArrowLeft"&&(m.currentTarget.selectionStart===0||m.currentTarget.selectionStart===0&&m.currentTarget.selectionEnd===m.currentTarget.value.length)&&(m.preventDefault(),u==="month"&&((x=a.current)==null||x.focus()),u==="year"&&((h=s.current)==null||h.focus()))}};return g.jsxs("div",{className:"flex items-center rounded-lg border px-1 text-sm",children:[g.jsx("input",{type:"text",ref:a,max:31,maxLength:2,value:n.day.toString(),onChange:l("day"),onKeyDown:b("day"),onFocus:u=>{window.innerWidth>1024&&u.target.select()},onBlur:w("day"),className:"w-7 border-none p-0 text-center outline-none",placeholder:"N"}),g.jsx("span",{className:"-mx-px opacity-20",children:"/"}),g.jsx("input",{type:"text",ref:s,max:12,maxLength:2,value:n.month.toString(),onChange:l("month"),onKeyDown:b("month"),onFocus:u=>{window.innerWidth>1024&&u.target.select()},onBlur:w("month"),className:"w-6 border-none p-0 text-center outline-none",placeholder:"T"}),g.jsx("span",{className:"-mx-px opacity-20",children:"/"}),g.jsx("input",{type:"text",ref:o,max:9999,maxLength:4,value:n.year.toString(),onChange:l("year"),onKeyDown:b("year"),onFocus:u=>{window.innerWidth>1024&&u.target.select()},onBlur:w("year"),className:"w-12 border-none p-0 text-center outline-none",placeholder:"NĂMNĂM"})]})};ze.displayName="DateInput";const rt=(e,t="vi-VN")=>e.toLocaleDateString(t,{month:"2-digit",day:"2-digit",year:"numeric"}),le=e=>{if(typeof e=="string"){const t=e.split("-").map(r=>parseInt(r,10));return new Date(t[0],t[1]-1,t[2])}else return e},Te=[{name:"today",label:"Hôm nay"},{name:"yesterday",label:"Hôm qua"},{name:"last7",label:"7 ngày trước"},{name:"thisMonth",label:"Tháng này"},{name:"lastMonth",label:"Tháng trước"}],lt=({initialDateFrom:e=new Date(new Date().setHours(0,0,0,0)),initialDateTo:t,onUpdate:n,align:r="end",locale:s="vi-VN"})=>{const[a,o]=D.useState(!1),[c,l]=D.useState({from:le(e),to:le(t||e)}),i=D.useRef(void 0),[w,b]=D.useState(void 0),[u,m]=D.useState(typeof window<"u"?window.innerWidth<960:!1);D.useEffect(()=>{const y=()=>{m(window.innerWidth<960)};return window.addEventListener("resize",y),()=>{window.removeEventListener("resize",y)}},[]);const f=y=>{const C=Te.find(({name:M})=>M===y);if(!C)throw new Error(`Unknown date range preset: ${y}`);const S=new Date,N=new Date;switch(C.name){case"today":S.setHours(0,0,0,0),N.setHours(23,59,59,999);break;case"yesterday":S.setDate(S.getDate()-1),S.setHours(0,0,0,0),N.setDate(N.getDate()-1),N.setHours(23,59,59,999);break;case"last7":S.setDate(S.getDate()-6),S.setHours(0,0,0,0),N.setHours(23,59,59,999);break;case"thisMonth":S.setDate(1),S.setHours(0,0,0,0),N.setHours(23,59,59,999);break;case"lastMonth":S.setMonth(S.getMonth()-1),S.setDate(1),S.setHours(0,0,0,0),N.setDate(0),N.setHours(23,59,59,999);break}return{from:S,to:N}},p=y=>{const C=f(y);l(C)},x=()=>{var y;for(const C of Te){const S=f(C.name),N=new Date(c.from);N.setHours(0,0,0,0);const M=new Date(S.from.setHours(0,0,0,0)),j=new Date(c.to??0);j.setHours(0,0,0,0);const A=new Date(((y=S.to)==null?void 0:y.setHours(0,0,0,0))??0);if(N.getTime()===M.getTime()&&j.getTime()===A.getTime()){b(C.name);return}}b(void 0)},h=()=>{l({from:typeof e=="string"?le(e):e,to:t?typeof t=="string"?le(t):t:typeof e=="string"?le(e):e})};D.useEffect(()=>{x()},[c]);const d=({preset:y,label:C,isSelected:S})=>g.jsx(se,{className:E(S&&"pointer-events-none"),variant:"ghost",onClick:()=>{p(y)},children:g.jsxs(g.Fragment,{children:[g.jsx("span",{className:E("pr-2 opacity-0",S&&"opacity-70"),children:g.jsx(Bt,{width:18,height:18})}),C]})}),T=(y,C)=>!y||!C?y===C:y.from.getTime()===C.from.getTime()&&(!y.to||!C.to||y.to.getTime()===C.to.getTime());return D.useEffect(()=>{a&&(i.current=c)},[a]),g.jsxs(Vt,{modal:!0,open:a,onOpenChange:y=>{y||h(),o(y)},children:[g.jsx(_t,{asChild:!0,children:g.jsxs(se,{size:"lg",variant:"outline",children:[g.jsx("div",{className:"text-right",children:g.jsx("div",{className:"py-1",children:g.jsx("div",{children:`${rt(c.from,s)}${c.to!=null?" - "+rt(c.to,s):""}`})})}),g.jsx("div",{className:"-mr-2 scale-125 pl-1 opacity-60",children:a?g.jsx(zt,{width:24}):g.jsx(Ht,{width:24})})]})}),g.jsxs(Ft,{align:r,className:"w-auto",children:[g.jsxs("div",{className:"flex py-2",children:[g.jsx("div",{className:"flex",children:g.jsxs("div",{className:"flex flex-col",children:[g.jsx("div",{className:"flex flex-col items-center justify-end gap-2 px-3 pb-4 lg:flex-row lg:items-start lg:pb-0",children:g.jsx("div",{className:"flex flex-col gap-2",children:g.jsxs("div",{className:"flex gap-2",children:[g.jsx(ze,{value:c.from,onChange:y=>{const C=c.to==null||y>c.to?y:c.to;l(S=>({...S,from:y,to:C}))}}),g.jsx("div",{className:"py-1",children:"-"}),g.jsx(ze,{value:c.to,onChange:y=>{const C=y<c.from?y:c.from;l(S=>({...S,from:C,to:y}))}})]})})}),u&&g.jsxs(Wt,{defaultValue:w,onValueChange:y=>{p(y)},children:[g.jsx(qt,{className:"mx-auto mb-2 w-[180px]",children:g.jsx($t,{placeholder:"Chọn..."})}),g.jsx(Kt,{children:Te.map(y=>g.jsx(Qt,{value:y.name,children:y.label},y.name))})]}),g.jsx("div",{children:g.jsx(Lt,{mode:"range",onSelect:y=>{(y==null?void 0:y.from)!=null&&l({from:y.from,to:y==null?void 0:y.to})},selected:c,numberOfMonths:u?1:2,defaultMonth:new Date(new Date().setMonth(new Date().getMonth()-(u?0:1))),locale:Er})})]})}),!u&&g.jsx("div",{className:"flex flex-col items-end gap-1 pr-2 pb-6 pl-6",children:g.jsx("div",{className:"flex w-full flex-col items-end gap-1 pr-2 pb-6 pl-6",children:Te.map(y=>g.jsx(d,{preset:y.name,label:y.label,isSelected:w===y.name},y.name))})})]}),g.jsxs("div",{className:"flex justify-end gap-2 py-2 pr-4",children:[g.jsx(se,{onClick:()=>{o(!1),h()},variant:"ghost",children:"Hủy"}),g.jsx(se,{onClick:()=>{o(!1),T(c,i.current)||n==null||n({range:c})},children:"Cập nhật"})]})]})]})};lt.displayName="DateRangePicker";lt.filePath="src/components/ui/date-range-picker.tsx";export{Qn as C,lt as D,$r as S,Kr as a,Qr as b,Gr as c,Xr as d,Yr as e,Ur as f,Jr as g,Br as h,Fr as i,qr as j,Wr as k,_r as l,Hr as m,Vr as n,Gn as o,Xn as p,Yn as q,Un as r,Jt as s,tn as t,Ce as u,Er as v,nn as w,rn as x,sn as y};
