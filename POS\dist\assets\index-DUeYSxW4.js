import{j as e,B as p,r as l,R as J,a4 as O,h as le,aS as ce,aT as oe}from"./index-sntk-7aJ.js";import{g as de}from"./error-utils-DlcdSg51.js";import"./pos-api-CQfNAror.js";import"./vietqr-api-C-lVxzD-.js";import{b as me,d as he}from"./use-item-types-DlwgINfg.js";import{u as pe}from"./use-items-Dp-DpVBb.js";import{u as xe}from"./use-removed-items-DUNjx3Y6.js";import{a as ue,b as ge}from"./use-item-categories-BW9jUbMR.js";import{u as fe}from"./use-printer-positions-data-CqLAEXE0.js";import"./user-B67nntxu.js";import"./crm-api-DhFa_vPG.js";import{P as ye}from"./modal-Yuq_-Dyf.js";import"./date-range-picker-CBqQlAZr.js";import{L as U}from"./form-2rWd-Izg.js";import{I as A}from"./input-DCw8aMl6.js";import{C as R}from"./checkbox-gkM78Twn.js";import{C as B,a as Q,b as $}from"./collapsible-DwXQUepC.js";import{D as W,a as Y,e as Z}from"./dialog-HHE8oSxh.js";import{C as H}from"./chevron-right-JsGDE6eB.js";import{C as K}from"./select-ZzLBlgJd.js";import{u as je}from"./use-printer-positions-CswkC9II.js";import{X as Ce}from"./calendar-BhUTNdpd.js";function ve({selectedItems:m,onItemSelection:o,getSelectedItemsDisplay:n,isFieldsDisabledAfterCreation:c}){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Thêm các món vào nhóm"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Các món đang thuộc nhóm khác sẽ được gán lại vào nhóm này."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(U,{className:"min-w-[200px] text-sm font-medium",children:"Áp dụng cho món"}),e.jsx("div",{className:"flex-1",children:e.jsx(p,{type:"button",variant:"outline",onClick:o,className:"w-full justify-start text-left",disabled:c,children:n()})})]})]})}function Ne({open:m,onOpenChange:o,items:n,selectedItems:c,onItemsSelected:S}){const[_,k]=l.useState(""),[j,T]=l.useState(!1),[a,M]=l.useState(!1),[x,P]=l.useState(c);console.log("ItemSelectionModal - selectedItems:",c),console.log("ItemSelectionModal - open:",m);const d=l.useMemo(()=>{if(!_.trim())return n;const i=_.toLowerCase();return n.filter(g=>g.item_name.toLowerCase().includes(i)||g.item_id.toLowerCase().includes(i))},[n,_]),C=d.filter(i=>x.includes(i.item_id)),v=d.filter(i=>!x.includes(i.item_id)),u=i=>{const g=x.includes(i)?x.filter(L=>L!==i):[...x,i];P(g)},t=()=>{S(x),o(!1)},h=()=>{P(c),o(!1)};return J.useEffect(()=>{m&&(P(c),k(""))},[m,c]),e.jsx(W,{open:m,onOpenChange:o,children:e.jsxs(Y,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(A,{placeholder:"Tìm kiếm món",value:_,onChange:i=>k(i.target.value),className:"w-full"})}),e.jsxs(B,{open:!j,onOpenChange:T,children:[e.jsx(Q,{asChild:!0,children:e.jsxs(p,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",C.length,")"]}),j?e.jsx(H,{className:"h-4 w-4"}):e.jsx(K,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[C.map(i=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!0,onCheckedChange:()=>u(i.item_id)}),e.jsx("span",{className:"text-sm",children:i.item_name})]},i.id)),C.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn món nào"})]})]}),e.jsxs(B,{open:!a,onOpenChange:M,children:[e.jsx(Q,{asChild:!0,children:e.jsxs(p,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",v.length,")"]}),a?e.jsx(H,{className:"h-4 w-4"}):e.jsx(K,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[v.map(i=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!1,onCheckedChange:()=>u(i.item_id)}),e.jsx("span",{className:"text-sm",children:i.item_name})]},i.id)),v.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có món nào"})]})]})]}),e.jsxs(Z,{children:[e.jsx(p,{variant:"outline",onClick:h,children:"Hủy"}),e.jsx(p,{onClick:t,children:"Lưu"})]})]})})}function we({showPrinterSection:m,selectedPrinters:o,onPrinterSelection:n,getSelectedPrintersDisplay:c,newlyCreatedCategory:S}){return m?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Vị trí máy in áp dụng"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Chọn các vị trí máy in sẽ áp dụng cho nhóm món này."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(U,{className:"min-w-[200px] text-sm font-medium",children:"Chọn vị trí máy in"}),e.jsx("div",{className:"flex-1",children:e.jsx(p,{type:"button",variant:"outline",onClick:n,className:"w-full justify-start text-left",children:c()})})]})]}):null}function _e({open:m,onOpenChange:o,printerPositions:n,selectedPrinters:c,onPrintersSelected:S,newlyCreatedCategory:_,currentCategory:k}){const[j,T]=l.useState(""),[a,M]=l.useState(!1),[x,P]=l.useState(!1),[d,C]=l.useState(c),[v,u]=l.useState(!1),t=je(),h=l.useMemo(()=>{if(!j.trim())return n;const s=j.toLowerCase();return n.filter(f=>f.printer_position_name.toLowerCase().includes(s)||f.printer_position_id.toLowerCase().includes(s))},[n,j]),i=h.filter(s=>d.includes(s.printer_position_id)),g=h.filter(s=>!d.includes(s.printer_position_id)),L=s=>{const f=d.includes(s)?d.filter(D=>D!==s):[...d,s];C(f)},E=async()=>{const s=_||k;if(!s){S(d),o(!1);return}u(!0);try{const f=new Set(c),D=new Set(d),N=d.filter(w=>!f.has(w)),z=c.filter(w=>!D.has(w)),y=[];N.forEach(w=>{const I=n.find(F=>F.printer_position_id===w);I&&y.push(t.mutateAsync({printerPosition:I,categoryId:s.item_type_id,action:"add"}))}),z.forEach(w=>{const I=n.find(F=>F.printer_position_id===w);I&&y.push(t.mutateAsync({printerPosition:I,categoryId:s.item_type_id,action:"remove"}))}),y.length>0&&(await Promise.all(y),O.success(`Đã cập nhật ${y.length} vị trí máy in cho nhóm món "${s.item_type_name}"`)),S(d),o(!1)}catch(f){console.error("Error updating printer positions:",f),O.error("Có lỗi xảy ra khi cập nhật vị trí máy in")}finally{u(!1)}},V=()=>{C(c),o(!1)};return J.useEffect(()=>{m&&(C(c),T(""))},[m,c]),e.jsx(W,{open:m,onOpenChange:o,children:e.jsxs(Y,{className:"max-w-md",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(A,{placeholder:"Tìm kiếm vị trí máy in",value:j,onChange:s=>T(s.target.value),className:"w-full"})}),e.jsxs(B,{open:!a,onOpenChange:M,children:[e.jsx(Q,{asChild:!0,children:e.jsxs(p,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",i.length,")"]}),a?e.jsx(H,{className:"h-4 w-4"}):e.jsx(K,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[i.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!0,onCheckedChange:()=>L(s.printer_position_id)}),e.jsx("span",{className:"text-sm",children:s.printer_position_name})]},s.printer_position_id)),i.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Chưa chọn vị trí máy in nào"})]})]}),e.jsxs(B,{open:!x,onOpenChange:P,children:[e.jsx(Q,{asChild:!0,children:e.jsxs(p,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",g.length,")"]}),x?e.jsx(H,{className:"h-4 w-4"}):e.jsx(K,{className:"h-4 w-4"})]})}),e.jsxs($,{className:"space-y-2",children:[g.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(R,{checked:!1,onCheckedChange:()=>L(s.printer_position_id)}),e.jsx("span",{className:"text-sm",children:s.printer_position_name})]},s.printer_position_id)),g.length===0&&e.jsx("div",{className:"p-2 text-sm text-gray-500",children:"Không có vị trí máy in nào"})]})]})]}),e.jsxs(Z,{children:[e.jsx(p,{variant:"outline",onClick:V,disabled:v,children:"Hủy"}),e.jsx(p,{onClick:E,disabled:v,children:v?"Đang cập nhật...":"Lưu"})]})]})})}function Xe({id:m}){const o=le(),n=!!m,{mutate:c,isPending:S}=ue(),{mutate:_,isPending:k}=me(),{mutate:j,isPending:T}=he(),{data:a,isLoading:M}=ge(m||"",n),{data:x=[]}=xe(),{data:P=[]}=pe({params:{skip_limit:!0,list_city_uid:x.map(r=>r.id).join(",")},enabled:x.length>0}),{data:d,isLoading:C,error:v}=fe({enabled:!0}),u=Array.isArray(d)?d:[];console.log("Category form printer positions debug:",{printerPositionsData:d,printerPositions:u,isPrinterPositionsLoading:C,printerPositionsError:v,length:u.length});const[t,h]=l.useState({name:"",code:"",followCategoryCreation:!1,displayOrder:void 0,selectedItems:[],selectedPrinters:[]}),[i,g]=l.useState(!1),[L,E]=l.useState(!1),[V,s]=l.useState(!1),[f,D]=l.useState(!1),[N,z]=l.useState(null);l.useEffect(()=>{if(n&&a&&u.length>0){const r=u.filter(b=>b.list_item_type_id.split(",").map(re=>re.trim()).filter(Boolean).includes(a.item_type_id)).map(b=>b.printer_position_id);h({name:a.item_type_name,code:a.item_type_id,followCategoryCreation:!1,displayOrder:a.sort||void 0,selectedItems:a.list_item||[],selectedPrinters:r}),D(!0)}},[n,a,u]),l.useEffect(()=>{N&&!n&&h(r=>({...r,code:N.item_type_id,followCategoryCreation:!0}))},[N,n]);const y=!n&&!!N,w=()=>{o({to:"/menu/categories/categories-in-brand"})},I=async()=>{if(q){if(N&&!n){o({to:"/menu/categories/categories-in-brand"});return}if(n&&a){const r={...a,item_type_name:t.name,sort:t.displayOrder||a.sort,extra_data:a.extra_data||{},list_item:t.selectedItems||[]};j(r,{onSuccess:()=>{t.selectedPrinters.length===0?s(!0):(O.success("Đã cập nhật nhóm món thành công"),o({to:"/menu/categories/categories-in-brand"}))}})}else c({name:t.name,code:t.followCategoryCreation&&t.code.trim()?t.code:void 0,sort:t.displayOrder,selectedItems:t.selectedItems},{onSuccess:r=>{z(r),s(!0)}})}},F=async()=>{if(!n||!a)return;const r={...a,active:a.active===1?0:1,extra_data:a.extra_data||{}};_(r,{onSuccess:()=>{const b=r.active===1?"kích hoạt":"vô hiệu hóa";O.success(`Đã ${b} nhóm món "${a.item_type_name}"`)},onError:b=>{const G=de(b);O.error(G)}})},q=t.name.trim()!=="",X=S||M||k||T,ee=()=>{g(!0)},te=r=>{h({...t,selectedItems:r}),g(!1)},se=()=>{E(!0)},ne=r=>{h({...t,selectedPrinters:r}),E(!1)},ae=()=>{s(!1),o({to:"/menu/categories/categories-in-brand"})},ie=()=>{s(!1),D(!0)};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(p,{variant:"ghost",size:"sm",onClick:w,className:"flex items-center",children:e.jsx(Ce,{className:"h-4 w-4"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[n&&a&&e.jsx(p,{type:"button",variant:a.active===1?"destructive":"default",disabled:X,className:"min-w-[100px]",onClick:F,children:a.active===1?"Deactivate":"Activate"}),(!n||(a==null?void 0:a.active)===1)&&e.jsx(p,{type:"button",disabled:X||!q,className:"min-w-[100px]",onClick:I,children:X?n?"Đang cập nhật...":"Đang tạo...":"Lưu"})]})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:n?"Chỉnh sửa nhóm món":"Tạo nhóm món"})})]}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(U,{htmlFor:"category-name",className:"min-w-[200px] text-sm font-medium",children:["Tên nhóm món ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(A,{id:"category-name",value:t.name,onChange:r=>h({...t,name:r.target.value}),placeholder:"Nhập tên nhóm món",className:"flex-1",disabled:y})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(U,{htmlFor:"category-code",className:"min-w-[200px] text-sm font-medium",children:"Mã nhóm món"}),e.jsx(A,{id:"category-code",value:t.code,onChange:r=>h({...t,code:r.target.value}),placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã nhóm món",disabled:!t.followCategoryCreation||n||y,className:"flex-1"}),!n&&!y&&e.jsx(R,{id:"follow-category-creation",checked:t.followCategoryCreation,onCheckedChange:r=>h({...t,followCategoryCreation:r,code:r?t.code:""})})]}),e.jsx(ve,{selectedItems:t.selectedItems,onItemSelection:ee,getSelectedItemsDisplay:()=>ce(t.selectedItems),isFieldsDisabledAfterCreation:y}),e.jsx(we,{showPrinterSection:f,selectedPrinters:t.selectedPrinters,onPrinterSelection:se,getSelectedPrintersDisplay:()=>oe(t.selectedPrinters),newlyCreatedCategory:N}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(U,{htmlFor:"display-order",className:"min-w-[200px] text-sm font-medium",children:"Thứ tự hiển thị"}),e.jsx(A,{id:"display-order",type:"number",value:t.displayOrder||"",onChange:r=>h({...t,displayOrder:r.target.value?Number(r.target.value):void 0}),placeholder:"Nhập số thứ tự hiển thị",className:"flex-1"})]})]})})}),e.jsx(Ne,{open:i,onOpenChange:g,items:P,selectedItems:t.selectedItems,onItemsSelected:te}),e.jsx(_e,{open:L,onOpenChange:E,printerPositions:u,selectedPrinters:t.selectedPrinters,onPrintersSelected:ne,newlyCreatedCategory:N,currentCategory:n?a:null}),e.jsx(ye,{open:V,onOpenChange:s,title:"Chưa có vị trí máy in nào áp dụng nhóm món, bạn có muốn chọn vị trí máy in?",cancelText:"Rời khỏi",confirmText:"Chọn thêm",onCancel:ae,onConfirm:ie,children:e.jsx("div",{className:"py-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Bạn có thể chọn các vị trí máy in để áp dụng cho nhóm món này."})})})]})}export{Xe as C};
